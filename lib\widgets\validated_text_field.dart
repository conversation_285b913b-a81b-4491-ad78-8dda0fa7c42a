import 'package:flutter/material.dart';
import '../services/validation_engine.dart';
import '../utils/font_manager.dart';
import '../utils/responsive_font_sizes.dart';

/// Validated Text Field Widget with Real-time Validation
class ValidatedTextField extends StatefulWidget {
  final String fieldName;
  final String label;
  final List<Map<String, dynamic>> validationRules;
  final Function(String, bool, String)? onValidationChanged;
  final TextEditingController? controller;
  final String? initialValue;
  final bool enabled;
  final TextInputType? keyboardType;
  final String? hintText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final int? maxLines;
  final int? maxLength;
  final EdgeInsets? contentPadding;
  final InputBorder? border;
  final Color? fillColor;
  final bool filled;

  const ValidatedTextField({
    Key? key,
    required this.fieldName,
    required this.label,
    required this.validationRules,
    this.onValidationChanged,
    this.controller,
    this.initialValue,
    this.enabled = true,
    this.keyboardType,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.maxLines = 1,
    this.maxLength,
    this.contentPadding,
    this.border,
    this.fillColor,
    this.filled = true,
  }) : super(key: key);

  @override
  State<ValidatedTextField> createState() => _ValidatedTextFieldState();
}

class _ValidatedTextFieldState extends State<ValidatedTextField> {
  late TextEditingController _controller;
  ValidationResult? _validationResult;
  bool _hasBeenTouched = false;
  
  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController(text: widget.initialValue);
    _controller.addListener(_onTextChanged);
    
    // Initial validation if there's initial value
    if (_controller.text.isNotEmpty) {
      _validateInput(_controller.text);
    }
  }
  
  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    super.dispose();
  }
  
  void _onTextChanged() {
    setState(() {
      _hasBeenTouched = true;
    });
    _validateInput(_controller.text);
  }
  
  void _validateInput(String value) {
    // Find validation rules for this field
    final fieldRules = widget.validationRules.where(
      (rule) => rule['attribute'] == widget.fieldName,
    ).toList();
    
    ValidationResult result;
    if (fieldRules.isNotEmpty) {
      // Apply first matching rule (you can modify to apply all rules)
      result = ValidationEngine.validateField(value, fieldRules.first);
    } else {
      // No validation rules - consider valid
      result = ValidationResult(
        isValid: true,
        message: '',
        rule: {},
      );
    }
    
    setState(() {
      _validationResult = result;
    });
    
    // Notify parent widget
    widget.onValidationChanged?.call(value, result.isValid, result.message);
  }
  
  Color _getBorderColor() {
    if (!_hasBeenTouched || _validationResult == null) {
      return const Color(0xFFE5E7EB); // Default gray
    }
    
    return _validationResult!.isValid 
        ? const Color(0xFF10B981) // Green for valid
        : const Color(0xFFEF4444); // Red for invalid
  }
  
  Color _getIconColor() {
    if (!_hasBeenTouched || _validationResult == null) {
      return Colors.grey[600]!;
    }
    
    return _validationResult!.isValid 
        ? const Color(0xFF10B981) // Green for valid
        : const Color(0xFFEF4444); // Red for invalid
  }
  
  Widget? _getValidationIcon() {
    if (!_hasBeenTouched || _validationResult == null || _controller.text.isEmpty) {
      return null;
    }
    
    return Icon(
      _validationResult!.isValid ? Icons.check_circle : Icons.error,
      color: _getIconColor(),
      size: 20,
    );
  }
  
  String? _getErrorText() {
    if (!_hasBeenTouched || _validationResult == null || _validationResult!.isValid) {
      return null;
    }
    
    return _validationResult!.message;
  }
  
  String? _getHelperText() {
    if (!_hasBeenTouched || _validationResult == null || !_validationResult!.isValid) {
      return null;
    }
    
    return _validationResult!.message.isNotEmpty ? _validationResult!.message : null;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (widget.label.isNotEmpty) ...[
          Text(
            widget.label,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
        ],
        
        // Text Field
        TextFormField(
          controller: _controller,
          enabled: widget.enabled,
          keyboardType: widget.keyboardType,
          obscureText: widget.obscureText,
          maxLines: widget.maxLines,
          maxLength: widget.maxLength,
          decoration: InputDecoration(
            hintText: widget.hintText,
            prefixIcon: widget.prefixIcon,
            suffixIcon: widget.suffixIcon ?? _getValidationIcon(),
            filled: widget.filled,
            fillColor: widget.fillColor ?? Colors.grey[50],
            contentPadding: widget.contentPadding ?? 
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            
            // Border styling based on validation state
            border: widget.border ?? OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: _getBorderColor()),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: _getBorderColor()),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: _getBorderColor(), width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFEF4444)),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFEF4444), width: 2),
            ),
            
            // Validation messages
            errorText: _getErrorText(),
            helperText: _getHelperText(),
            errorStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: const Color(0xFFEF4444),
            ),
            helperStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: const Color(0xFF10B981),
            ),
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }
}

/// Validation Summary Widget - Shows overall form validation status
class ValidationSummary extends StatelessWidget {
  final Map<String, ValidationResult> validationResults;
  final bool showOnlyErrors;
  
  const ValidationSummary({
    Key? key,
    required this.validationResults,
    this.showOnlyErrors = true,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final errors = validationResults.entries
        .where((entry) => !entry.value.isValid)
        .toList();
    
    final successes = validationResults.entries
        .where((entry) => entry.value.isValid && entry.value.message.isNotEmpty)
        .toList();
    
    if (showOnlyErrors && errors.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: errors.isNotEmpty ? Colors.red[50] : Colors.green[50],
        border: Border.all(
          color: errors.isNotEmpty ? Colors.red[200]! : Colors.green[200]!,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                errors.isNotEmpty ? Icons.error : Icons.check_circle,
                color: errors.isNotEmpty ? Colors.red[600] : Colors.green[600],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                errors.isNotEmpty 
                    ? 'Validation Errors (${errors.length})'
                    : 'All Fields Valid',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: errors.isNotEmpty ? Colors.red[800] : Colors.green[800],
                ),
              ),
            ],
          ),
          
          // Error messages
          if (errors.isNotEmpty) ...[
            const SizedBox(height: 12),
            ...errors.map((entry) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                '• ${entry.key}: ${entry.value.message}',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.red[700],
                ),
              ),
            )),
          ],
          
          // Success messages (if not showing only errors)
          if (!showOnlyErrors && successes.isNotEmpty) ...[
            const SizedBox(height: 12),
            ...successes.map((entry) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                '• ${entry.key}: ${entry.value.message}',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.green[700],
                ),
              ),
            )),
          ],
        ],
      ),
    );
  }
}
