import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/providers/roles_provider.dart';
import 'package:nsl/providers/go_details_provider.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:nsl/models/solution_creation_model.dart';
import 'package:nsl/models/role_model.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'dart:math' as math;
import 'package:provider/provider.dart';



/// Helper class for column width constraints
class ColumnConstraints {
  final double minWidth;
  final double maxWidth;

  const ColumnConstraints({
    required this.minWidth,
    required this.maxWidth,
  });
}

class ExtractGoDetailsMiddleStatic extends StatefulWidget {
  final String? sessionId; // New session-based API support
  final String? userIntent;

  const ExtractGoDetailsMiddleStatic({
    super.key,
    this.sessionId,
    this.userIntent,
  });

  @override
  State<ExtractGoDetailsMiddleStatic> createState() =>
      _ExtractGoDetailsMiddleStaticState();
}

class _ExtractGoDetailsMiddleStaticState
    extends State<ExtractGoDetailsMiddleStatic> {
  late AccordionController _accordionController;

  // Simple scroll controller without complex synchronization
  final ScrollController _scrollController = ScrollController();

  // Shared scroll controllers for Excel-like frozen pane behavior
  final ScrollController _verticalController = ScrollController();
  final ScrollController _actionsController = ScrollController();

 

  @override
  void initState() {
    super.initState();
    // Initialize roles provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final rolesProvider = Provider.of<RolesProvider>(context, listen: false);
      rolesProvider.fetchRoles();
    });
  }






  @override
  Widget build(BuildContext context) {
    return Consumer4<WebHomeProviderStatic, ObjectCreationProvider, RolesProvider, GoDetailsProvider>(
      builder: (context, provider, objectCreationProvider, rolesProvider, goDetailsProvider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Header with toggle
              _buildHeader(context, provider),

              // Content area
              Expanded(
                child: _buildContent(context, provider, rolesProvider, goDetailsProvider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000), // Black with 10% opacity
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            // provider.isAIMode ? 'Objects' : 'Extracted Details',
            'Extracted Details',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleSmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.white,
              fontWeight: FontWeight.bold,
              height: 1,
            ),
          ),

          // Right side with toggle and manually process text
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [

              // AI/Manual Toggle
              _buildAIManualToggle(context, provider),

              const SizedBox(width: 16),

              // // Manually Process text
              // Text(
              //   'Manually Process',
              //   style: FontManager.getCustomStyle(
              //     fontSize: ResponsiveFontSizes.titleSmall(context),
              //     color: Colors.white,
              //     fontWeight: FontWeight.w400,
              //     fontFamily: FontManager.fontFamilyTiemposText,
              //     height: 1,
              //   ),
              // ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
       

        // Toggle switch
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              // Close entity details panel if it's open before switching modes
              final manualProvider =
                  Provider.of<ManualCreationProvider>(context, listen: false);
              if (manualProvider.selectedEntity != null) {
                manualProvider.setSelectedEntity(null);
              }

              provider.toggleAIMode();
              manualProvider.handleEntityValidationForBook();
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(width: 4),

        // ai label on the right
        Text(
          'Form',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
        const SizedBox(width: 12),
         // manua; label on the left
        Text(
          'Manual Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
       
      ],
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider,
      RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    return Container(
     
      padding: const EdgeInsets.all(12),
      child: _buildContentWithLineNumbers(context, rolesProvider, goDetailsProvider),
    );
  }

  Widget _buildContentWithLineNumbers(BuildContext context, RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    int lineNumber = 1;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Line 1: Solution row
        _buildLineWithNumber(lineNumber++, _buildFirstRow(context, rolesProvider, goDetailsProvider)),

        const SizedBox(height: 16),

        // Line 2: Description row
        _buildLineWithNumber(lineNumber++, _buildSecondRow(context, goDetailsProvider)),

        // Show local objectives section after validation
        if (goDetailsProvider.currentStep == GoDetailsStep.afterValidation ||
            goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) ...[
          const SizedBox(height: 24),

          // Line 3: LOCAL OBJECTIVES header
          _buildLineWithNumber(lineNumber++, _buildLocalObjectivesHeader()),

          const SizedBox(height: 8),

          // Local objectives content with line numbers
          if (goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) ...[
            // Show LO list with line numbers
            ...goDetailsProvider.localObjectives.asMap().entries.expand((entry) {
              final index = entry.key;
              final objective = entry.value;
              final widgets = <Widget>[
                _buildLineWithNumber(
                  lineNumber++,
                  _buildLocalObjectiveItem(index, objective, goDetailsProvider),
                ),
              ];

              // Add pathway creation fields if open for this LO
              if (goDetailsProvider.isPathwayCreationOpen(index)) {
                widgets.addAll(_buildPathwayCreationFields(index, goDetailsProvider, rolesProvider, lineNumber));
                lineNumber += _getPathwayFieldsCount(index, goDetailsProvider);
              }

              return widgets;
            }),
          ] else ...[
            // Line 4: Input field
            _buildLineWithNumber(lineNumber++, _buildLocalObjectiveInput(context, goDetailsProvider)),
          ],
        ],
      ],
    );
  }

  Widget _buildFirstRow(BuildContext context, RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        // Solution label and text field
        Expanded(
          flex: 3,
          child: Row(
            children: [
              const Text(
                'Solution:',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText
                ),
              ),
              const SizedBox(width: AppSpacing.xs),
              Expanded(
                child: Container(
                    height: 24,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: TextField(
                    
                    controller: goDetailsProvider.solutionController,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                          fillColor: Colors.transparent, 
                          hoverColor: Colors.transparent, 
                      contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 7), // Remove default padding
        isDense: true,
                      //  contentPadding: EdgeInsets.symmetric(horizontal: 12,),
                      // hintText: 'Enter solution name',
                    ),
                    style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText
                ),
                  ),
                ),
              ),
              const SizedBox(width: AppSpacing.xs),
              // Tick icon button
              InkWell(
                onTap: goDetailsProvider.isValidating ? null : () {
                  goDetailsProvider.validateSolution();
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: const Color(0xFF007AFF),
                     shape: BoxShape.circle,
                  ),
                  child:
                  //  goDetailsProvider.isValidating
                  //     ? const SizedBox(
                  //         width: 16,
                  //         height: 16,
                  //         child: CircularProgressIndicator(
                  //           strokeWidth: 2,
                  //           valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  //         ),
                  //       )
                  //     : 
                      const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 14,
                        ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(width: 24),

        // Agent Type dropdown
        Expanded(
          flex: 1,
          child: _buildAgentTypeDropdown(context, rolesProvider, goDetailsProvider),
        ),
      ],
    );
  }

  Widget _buildAgentTypeDropdown(BuildContext context, RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        
        Expanded(
          child: Container(
            height: 24,
             padding: EdgeInsets.symmetric(horizontal: 8, ),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
              color: Colors.white,
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<PostgresRole>(
                value: goDetailsProvider.selectedRole,
                hint: Text(
                  'Agent Type',
                  style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w700,
                  color: Colors.grey.shade600,
                  fontFamily: FontManager.fontFamilyTiemposText
                ),
                 
                ),
                isExpanded: true,
                icon: Icon(Icons.keyboard_arrow_down, color: Colors.grey.shade600, size: 12),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText
                ),
                dropdownColor: Colors.white,
                items: 
                rolesProvider.isLoading
                    ? [
                        const DropdownMenuItem<PostgresRole>(
                          value: null,
                          child: Text('Loading roles...', style: TextStyle(fontSize: 14)),
                        )
                      ]
                    : rolesProvider.roles.isEmpty
                        ? [
                            const DropdownMenuItem<PostgresRole>(
                              value: null,
                              child: Text('No roles available', style: TextStyle(fontSize: 14)),
                            )
                          ]
                        : 
                        rolesProvider.roles.map((role) {
                            return DropdownMenuItem<PostgresRole>(
                              value: role,
                              child: Text(
                                role.name ?? 'Unknown Role',
                                style: const TextStyle(fontSize: 14, color: Colors.black),
                              ),
                            );
                          }).toList(),
                onChanged:
                 rolesProvider.isLoading ? null : 
                 (PostgresRole? selectedRole) {
                  goDetailsProvider.setSelectedRole(selectedRole);
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSecondRow(BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        const Text(
          'Description:',
           style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText
                ),
        ),
        const SizedBox(width: AppSpacing.xs),
        Expanded(
          child: Container(
            height: 24,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
            ),
            child: TextField(
              controller: goDetailsProvider.descriptionController,
              decoration: const InputDecoration(
                border: InputBorder.none,
                isDense: true,
                enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                          fillColor: Colors.transparent, 
                          hoverColor: Colors.transparent, 
                      contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 7), // Remove default padding
     
                // hintText: 'Enter description',
              ),
             style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText
                ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLocalObjectivesSection(BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // LOCAL OBJECTIVES header
        const Row(
          children: [
            Text(
              'LOCAL OBJECTIVES',
              style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText
                ),),
          ],
        ),
        const SizedBox(height: AppSpacing.xs),
        // Local objectives content - show input or list based on step
        if (goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) ...[
          // Show LO list
          _buildLocalObjectivesList(context, goDetailsProvider),
        ] else ...[
          // Show input field
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 24,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: TextField(
                    controller: goDetailsProvider.localObjectiveController,
                    decoration: const InputDecoration(
                       border: InputBorder.none,
                  isDense: true,
                  enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                            fillColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                        contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 7),
                      hintText: 'Type LO name with full stop (.)',
                      hintStyle:  TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText
                  ),
                    ),
                    style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText
                  ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Tick icon for local objective
              InkWell(
                onTap: () {
                  goDetailsProvider.processLocalObjectives();
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: const Color(0xFF007AFF),
                   shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildLocalObjectivesList(BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Build LO items
        ...goDetailsProvider.localObjectives.asMap().entries.map((entry) {
          final index = entry.key;
          final objective = entry.value;

          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Row(
              children: [
                // LO number and text
                Expanded(
                  child: Text(
                    'LO-${index + 1}. $objective',
                    style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Create pathway button
                InkWell(
                  onTap: () {
                    // Handle create pathway for this LO
                    print('Create pathway clicked for LO-${index + 1}: $objective');
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'Create pathway',
                      style: TextStyle(
                        fontSize: 8,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                
              ],
            ),
          );
        }),
      ],
    );
  }

  // Helper methods for line numbers
  Widget _buildLineWithNumber(int lineNumber, Widget content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Line number
        Container(
          // color: Color(0xFFEDF3FF),
          width: 20,
          child: Text(
            '$lineNumber',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyInter,
              
            ),
          ),
        ),
        // Content
        Expanded(child: content),
      ],
    );
  }

  Widget _buildLocalObjectivesHeader() {
    return const Text(
      'LOCAL OBJECTIVES',
      style: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w700,
        color: Colors.black,
        fontFamily: FontManager.fontFamilyTiemposText,
      ),
    );
  }

  Widget _buildLocalObjectiveItem(int index, String objective, GoDetailsProvider goDetailsProvider) {
    return Padding(
    padding: const EdgeInsets.only(bottom: AppSpacing.xs),
      // crossAxisAlignment: CrossAxisAlignment.center,
    
      child:  Row(
           mainAxisAlignment: MainAxisAlignment.start,
          children: [
            // LO number and text
            Text(
              'LO-${index + 1}. $objective',
             
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
            const SizedBox(width: AppSpacing.xs),
            // Create pathway button
            InkWell(
              onTap: () {
                goDetailsProvider.togglePathwayCreation(index);
              },
              child: const Text(
                'Create pathway',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue,
                ),
              ),
            ),
              const SizedBox(width: 8),
                 InkWell(
          onTap: () {
           
          },
          child: Container(
            width: 18,
            height: 18,
            decoration: const BoxDecoration(
              color: Color(0xFF007AFF),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.add,
              color: Colors.white,
              size: 12,
            ),
          ),
        ),
          ],

        ),
    );
     
    
  }

  Widget _buildLocalObjectiveInput(BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 24,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
            ),
            child: TextField(
              controller: goDetailsProvider.localObjectiveController,
              decoration: const InputDecoration(
                border: InputBorder.none,
                isDense: true,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 7),
                hintText: 'Type LO name with full stop (.)',
                hintStyle: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText
                ),
              ),
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        // Tick icon for local objective
        InkWell(
          onTap: () {
            goDetailsProvider.processLocalObjectives();
          },
          child: Container(
            width: 24,
            height: 24,
            decoration: const BoxDecoration(
              color: Color(0xFF007AFF),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check,
              color: Colors.white,
              size: 14,
            ),
          ),
        ),
      ],
    );
  }

  // Pathway creation helper methods
  List<Widget> _buildPathwayCreationFields(int loIndex, GoDetailsProvider goDetailsProvider, RolesProvider rolesProvider, int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;

    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);

    if (selectedType == 'Alternative' || selectedType == 'Parallel') {
      // Row 1: Main pathway fields + Apply Condition header only
      widgets.add(_buildLineWithNumber(
        currentLineNumber++,
        _buildMainRowWithApplyConditionHeader(loIndex, goDetailsProvider, rolesProvider),
      ));

      // Row 2: Second LO + first condition row
      widgets.add(_buildLineWithNumber(
        currentLineNumber++,
        _buildSecondRowWithCondition(loIndex, goDetailsProvider, 0),
      ));

      // Row 3: Empty space + second condition row
      widgets.add(_buildLineWithNumber(
        currentLineNumber++,
        _buildThirdRowWithCondition(loIndex, goDetailsProvider, 1),
      ));

      // Row 4: + LO button
      widgets.add(_buildLineWithNumber(
        currentLineNumber++,
        _buildAddLOButton(),
      ));
    } else {
      // For Sequential, Recursive, Terminal - just the main row
      widgets.add(_buildLineWithNumber(
        currentLineNumber++,
        _buildPathwayFieldsRow(loIndex, goDetailsProvider, rolesProvider),
      ));
    }

    return widgets;
  }

  int _getPathwayFieldsCount(int loIndex, GoDetailsProvider goDetailsProvider) {
    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);
    if (selectedType == 'Alternative' || selectedType == 'Parallel') {
      return 4; // Main row with header + Second row with condition + Third row with condition + +LO row
    }
    return 1; // Just the main row for Sequential, Recursive, Terminal
  }

  // Build main row with pathway fields + Apply Condition header only
  Widget _buildMainRowWithApplyConditionHeader(int loIndex, GoDetailsProvider goDetailsProvider, RolesProvider rolesProvider) {
    return Row(
      children: [
        // Select Role dropdown
        SizedBox(
          width: 100,
          child: _buildPathwayRoleField(loIndex, goDetailsProvider, rolesProvider),
        ),
        const SizedBox(width: 8),

        // Select Type dropdown
        SizedBox(
          width: 100,
          child: _buildPathwayTypeField(loIndex, goDetailsProvider),
        ),
        const SizedBox(width: 8),

        // Select LO dropdown
        SizedBox(
          width: 100,
          child: _buildPathwayLOField(loIndex, goDetailsProvider),
        ),
        const SizedBox(width: 16),

        // Apply Condition header only
        SizedBox(
          width: 100,
          child: Container(
            height: 24,
            alignment: Alignment.center,
            child: const Text(
              'Apply Condition',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w700,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Build second row with second LO + first condition row
  Widget _buildSecondRowWithCondition(int loIndex, GoDetailsProvider goDetailsProvider, int conditionRowIndex) {
    return Row(
      children: [
        // Empty space to align with dropdowns above
        const SizedBox(width: 100),
        const SizedBox(width: 8),
        const SizedBox(width: 100),
        const SizedBox(width: 8),

        // Second Select LO dropdown
        SizedBox(
          width: 100,
          child: _buildPathwayLOField(loIndex, goDetailsProvider),
        ),
        const SizedBox(width: 16),

        // Entity Attribute dropdown
        SizedBox(
          width: 100,
          child: _buildEntityAttributeDropdown(),
        ),
        const SizedBox(width: 8),

        // Condition dropdown
        SizedBox(
          width: 100,
          child: _buildConditionDropdown(),
        ),
        const SizedBox(width: 8),

        // Entity Attribute dropdown (second)
        SizedBox(
          width: 100,
          child: _buildEntityAttributeDropdown(),
        ),
      ],
    );
  }

  // Build third row with empty space + second condition row
  Widget _buildThirdRowWithCondition(int loIndex, GoDetailsProvider goDetailsProvider, int conditionRowIndex) {
    return Row(
      children: [
        // Empty space to align with dropdowns above
        const SizedBox(width: 100),
        const SizedBox(width: 8),
        const SizedBox(width: 100),
        const SizedBox(width: 8),

        // Empty space for LO alignment
        const SizedBox(width: 100),
        const SizedBox(width: 16),

        // Entity Attribute dropdown
        SizedBox(
          width: 100,
          child: _buildEntityAttributeDropdown(),
        ),
        const SizedBox(width: 8),

        // Condition dropdown
        SizedBox(
          width: 100,
          child: _buildConditionDropdown(),
        ),
        const SizedBox(width: 8),

        // Entity Attribute dropdown (second)
        SizedBox(
          width: 100,
          child: _buildEntityAttributeDropdown(),
        ),
      ],
    );
  }

  Widget _buildPathwayFieldsRow(int loIndex, GoDetailsProvider goDetailsProvider, RolesProvider rolesProvider) {
    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);

    return Row(
      children: [
        // Select Role dropdown
        SizedBox(
          width: 100,
          child: _buildPathwayRoleField(loIndex, goDetailsProvider, rolesProvider),
        ),
        const SizedBox(width: 8),

        // Select Type dropdown
        SizedBox(
          width: 100,
          child: _buildPathwayTypeField(loIndex, goDetailsProvider),
        ),

        // Third field based on selected type
        if (selectedType == 'Sequential') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: 100,
            child: _buildPathwayLOField(loIndex, goDetailsProvider),
          ),
        ] else if (selectedType == 'Alternative' || selectedType == 'Parallel') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: 100,
            child: _buildPathwayLOField(loIndex, goDetailsProvider),
          ),
        ] else if (selectedType == 'Recursive') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: 100,
            child: _buildRecursiveInputField(loIndex, goDetailsProvider),
          ),
        ],
        // Terminal type has no third field
      ],
    );
  }

  // Helper method for Entity Attribute dropdown
  Widget _buildEntityAttributeDropdown() {
    return Container(
      height: 24,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(2),
      ),
      child: DropdownButton<String>(
        hint: const Text(
          'Entity Attribute',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        isExpanded: true,
        underline: const SizedBox(),
        dropdownColor: Colors.white,
        items: const [], // Add your entity attributes here
        onChanged: (String? value) {
          // Handle entity attribute selection
        },
      ),
    );
  }

  // Helper method for Condition dropdown
  Widget _buildConditionDropdown() {
    return Container(
      height: 24,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(2),
      ),
      child: DropdownButton<String>(
        hint: const Text(
          'Condition',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        isExpanded: true,
        underline: const SizedBox(),
        dropdownColor: Colors.white,
        items: const [], // Add your conditions here
        onChanged: (String? value) {
          // Handle condition selection
        },
      ),
    );
  }

  Widget _buildPathwayRoleField(int loIndex, GoDetailsProvider goDetailsProvider, RolesProvider rolesProvider) {
    return Container(
      height: 24,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(2),
      ),
      child: DropdownButton<PostgresRole>(
        value: goDetailsProvider.getPathwaySelectedRole(loIndex),
        hint: const Text(
          'Select Role',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        isExpanded: true,
        underline: const SizedBox(),
        dropdownColor: Colors.white,
        items: rolesProvider.isLoading
            ? [
                const DropdownMenuItem<PostgresRole>(
                  value: null,
                  child: Text('Loading roles...', style: TextStyle(fontSize: 10)),
                )
              ]
            : rolesProvider.roles.isEmpty
                ? [
                    const DropdownMenuItem<PostgresRole>(
                      value: null,
                      child: Text('No roles available', style: TextStyle(fontSize: 10)),
                    )
                  ]
                : rolesProvider.roles.map((role) {
                    return DropdownMenuItem<PostgresRole>(
                      value: role,
                      child: Text(
                        role.name ?? 'Unknown Role',
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                    );
                  }).toList(),
        onChanged: rolesProvider.isLoading ? null : (PostgresRole? selectedRole) {
          goDetailsProvider.setPathwaySelectedRole(loIndex, selectedRole);
        },
      ),
    );
  }

  Widget _buildPathwayTypeField(int loIndex, GoDetailsProvider goDetailsProvider) {
    final typeOptions = ['Sequential', 'Alternative', 'Parallel', 'Recursive', 'Terminal'];

    return Container(
      height: 24,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(2),
      ),
      child: DropdownButton<String>(
        value: goDetailsProvider.getPathwaySelectedType(loIndex),
        hint: const Text(
          'Select Type',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        isExpanded: true,
        underline: const SizedBox(),
        dropdownColor: Colors.white,
        items: typeOptions.map((type) {
          return DropdownMenuItem<String>(
            value: type,
            child: Text(
              type,
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          );
        }).toList(),
        onChanged: (String? selectedType) {
          goDetailsProvider.setPathwaySelectedType(loIndex, selectedType);
        },
      ),
    );
  }

  Widget _buildPathwayLOField(int loIndex, GoDetailsProvider goDetailsProvider) {
    final availableLOs = goDetailsProvider.getAvailableLOsForSequential(loIndex);

    return Container(
      height: 24,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(2),
      ),
      child: DropdownButton<String>(
        value: goDetailsProvider.getPathwaySelectedLO(loIndex),
        hint: const Text(
          'Select LO',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        isExpanded: true,
        underline: const SizedBox(),
        dropdownColor: Colors.white,
        items: availableLOs.isEmpty
            ? [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('No LOs available', style: TextStyle(fontSize: 10)),
                )
              ]
            : availableLOs.map((lo) {
                return DropdownMenuItem<String>(
                  value: lo,
                  child: Text(
                    lo,
                    style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                );
              }).toList(),
        onChanged: availableLOs.isEmpty ? null : (String? selectedLO) {
          goDetailsProvider.setPathwaySelectedLO(loIndex, selectedLO);
        },
      ),
    );
  }

  // Additional pathway creation helper methods
  Widget _buildRecursiveInputField(int loIndex, GoDetailsProvider goDetailsProvider) {
    return Container(
      height: 24,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(2),
      ),
      child: TextField(
        decoration: const InputDecoration(
          border: InputBorder.none,
          isDense: true,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          fillColor: Colors.transparent,
          hoverColor: Colors.transparent,
          contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 7),
          hintText: 'Input number',
          hintStyle: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        style: const TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: Colors.black,
          fontFamily: FontManager.fontFamilyTiemposText,
        ),
      ),
    );
  }

  Widget _buildAddLOButton() {
    return Row(
      children: [
        const SizedBox(width: 100),
        const SizedBox(width: 8),
        const SizedBox(width: 100),
        const SizedBox(width: 8),
        SizedBox(
          width: 100,
          child: const Text(
            '+ LO',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.blue,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
      ],
    );
  }


}