import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:nsl/models/RoleCreatModel.dart';
import 'package:nsl/models/roles_expansion_model.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:provider/provider.dart';

class RoleExpensionPanelDetailsStatic extends StatefulWidget {
  final String? sessionId;
  final String? userIntent;

  const RoleExpensionPanelDetailsStatic({
    super.key,
    this.sessionId,
    this.userIntent,
  });

  @override
  State<RoleExpensionPanelDetailsStatic> createState() =>
      _RoleExpensionPanelDetailsStaticState();
}

class _RoleExpensionPanelDetailsStaticState
    extends State<RoleExpensionPanelDetailsStatic> {
     final Map<String, dynamic> data = {
    "roles": [
      {
        "roleName": "CEO",
        "role_configuration": {
          "roleName": "CEO",
          "description": "Chief Executive Officer - leads entire organization",
          "reportsTo": "Board of Directors",
          "organizationLevel": "Executive",
          "department": "Executive",
          "inherits": "Executive"
        },
        "department_configuration": {
          "departmentName": "Executive",
          "description": "Executive leadership and strategic direction",
          "departmentHeadRole": "CEO",
          "parentDepartment": null
        },
        "role_inheritance": {
          "parentRole": "Executive",
          "inheritsRole": "CEO"
        }
      }
    ],
    "tenantID": "acme_corp_001",
    "tenantName": "Acme Corporation"
  };


  // Parse and extract roles
 List<Role> parseRolesFromJson(Map<String, dynamic> json) {
  final rolesJson = json['roles'] as List;
  return rolesJson.map((r) => Role.fromJson(r)).toList();
}
  late AccordionController _accordionController;
  final TextEditingController _departmentNameController =
      TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  String _selectedDepartmentHead = 'VP Marketing';
  String _selectedParentDepartment = 'None';
    List<Role> roles= [];

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
      roles = parseRolesFromJson(data);
      print('roless ${roles[0].roleConfiguration.roleName}');
    _accordionController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _accordionController.dispose();
    _departmentNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// Helper method to get status text based on count
  String _getStatusFromCount(int count) {
    if (count == 0) {
      return 'Missing';
    } else if (count > 0 && count < 5) {
      return 'Partial Completion';
    } else {
      return 'Completed';
    }
  }

  /// Helper method to get background color based on count
  Color _getBackgroundColorFromCount(int count) {
    if (count == 0) {
      return const Color(0xFFFEE2E2); // Red background for missing
    } else if (count > 0 && count < 5) {
      return const Color(0xFFFEF3C7); // Yellow background for partial
    } else {
      return const Color(0xFFD1FAE5); // Green background for completed
    }
  }

  /// Helper method to get text color based on count
  Color _getTextColorFromCount(int count) {
    if (count == 0) {
      return const Color(0xFF991B1B); // Red text for missing
    } else if (count > 0 && count < 5) {
      return const Color(0xFF92400E); // Yellow text for partial
    } else {
      return const Color(0xFF065F46); // Green text for completed
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.white,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(6.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Static single expansion panel
            _buildObjectExpansionPanel(context, 'Role Configuration'),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectExpansionPanel(BuildContext context, String objectTitle) {
    bool isExpanded = true; // Static expanded state

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 1),
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ListTileTheme(
          dense: true,
          child: ExpansionTile(
            initiallyExpanded: true,
            tilePadding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
            childrenPadding: EdgeInsets.zero,
            trailing: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color:
                    isExpanded ? const Color(0xFF0058FF) : Colors.transparent,
              ),
              child: Icon(
                isExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: isExpanded ? Colors.white : Colors.grey[600],
                size: 20,
              ),
            ),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    objectTitle,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                      height: 1.2,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                HoverBellIcon(
                  onTap: () {
                    // Bell icon click action - can be customized as needed
                  },
                ),
              ],
            ),
            children: [
              _buildObjectDetailsSection(context, objectTitle),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildObjectDetailsSection(BuildContext context, String objectTitle) {
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 0, 4, 0),
      child: Column(
        children: [
          // Static accordion items with sample data
          _buildSimpleAccordionItem(
            context,
            'Role Configuration',
            '12 Detected',
            'E-commerce Org',
            const Color(0xFFD1FAE5),
            const Color(0xFF065F46),
          ),
          _buildSimpleAccordionItem(
            context,
            'Departments',
            '6 Found',
            'Structured Hierarchy',
            const Color(0xFFD1FAE5),
            const Color(0xFF065F46),
          ),
          _buildSimpleAccordionItem(
            context,
            'Role Inheritance',
            '3 Detected',
            'Permission Flow',
            const Color(0xFFFEE2E2),
            const Color(0xFF991B1B),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor,
  ) {
    final isExpanded = _accordionController.isPanelExpanded('simple_$title');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              _accordionController.togglePanel('simple_$title');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title Text
                        Flexible(
                          flex: 3,
                          child: Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w300,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.visible,
                            maxLines: 2,
                          ),
                        ),

                        const SizedBox(width: 8),

                        // Status Badge
                        Flexible(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: backgroundColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              status,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: textColor,
                              ),
                              overflow: TextOverflow.visible,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 150,
                    alignment: Alignment.centerRight,
                    child: Text(
                      count,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) ...[
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: _buildPlaceholderContent(context, title),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPlaceholderContent(BuildContext context, String title) {
    switch (title) {
      case 'Role Configuration':
        return _buildRoleConfigurationContent(context);
      case 'Departments':
        return _buildDepartmentsContent(context);
      case 'Role Inheritance':
        return _buildRoleInheritanceContent(context);
      default:
        return Container();
    }
  }

  Widget _buildRoleConfigurationContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Role Configuration',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
              ElevatedButton.icon(
                onPressed: () => _showAddRoleModal(context),
                icon: const Icon(
                  Icons.add,
                  size: 16,
                  color: Colors.white,
                ),
                label: Text(
                  'Add Role',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF007AFF),
                  elevation: 0,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              children: [
                // Table Header
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Role Name',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Description',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Reports To',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                       Expanded(
                        flex: 2,
                        child: Text(
                          'organizationLevel',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                       Expanded(
                        flex: 2,
                        child: Text(
                          'department',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                       Expanded(
                        flex: 2,
                        child: Text(
                          'inherits',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Actions',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Table Row
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Colors.grey[300]!),
                    ),
                  ),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: roles.length,
                    itemBuilder: (context,index){
                    return Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          '${roles[index].roleConfiguration.roleName}',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          '${roles[index].roleConfiguration.description}',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          '${roles[index].roleConfiguration.reportsTo}',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                       Expanded(
                        flex: 2,
                        child: Text(
                          '${roles[index].roleConfiguration.organizationLevel}',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                       Expanded(
                        flex: 2,
                        child: Text(
                          '${roles[index].roleConfiguration.department}',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                       Expanded(
                        flex: 2,
                        child: Text(
                          '${roles[index].roleConfiguration.inherits}',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Row(
                          children: [
                            InkWell(
                              onTap: () => _showEditRoleModal(context),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  Icons.edit_outlined,
                                  size: 16,
                                  color: Colors.blue[600],
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            InkWell(
                              onTap: () {},
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  Icons.delete_outline,
                                  size: 16,
                                  color: Colors.red[600],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                  })
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDepartmentsContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Department Configuration section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Department Configuration',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _showAddDepartmentModal(context),
                icon: const Icon(
                  Icons.add,
                  size: 16,
                  color: Colors.white,
                ),
                label: Text(
                  'Add Department',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF007AFF),
                  elevation: 0,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              children: [
                // Table Header
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Department Name',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Description',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Department Head Role',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Parent Department',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Actions',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Table Row
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Colors.grey[300]!),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          roles[0].departmentConfiguration.departmentName,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          roles[0].departmentConfiguration.description,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          roles[0].departmentConfiguration.departmentHeadRole,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          roles[0].departmentConfiguration.parentDepartment.toString(),
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Row(
                          children: [
                            InkWell(
                              onTap: () => _showAddDepartmentModal(context),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  Icons.edit_outlined,
                                  size: 16,
                                  color: Colors.blue[600],
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            InkWell(
                              onTap: () {},
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  Icons.delete_outline,
                                  size: 16,
                                  color: Colors.red[600],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleInheritanceContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Inheritance Configuration section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Inheritance Configuration',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  _showAddInheritanceModal(context);
                },
                icon: const Icon(
                  Icons.add,
                  size: 16,
                  color: Colors.white,
                ),
                label: Text(
                  'Add Inheritance',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF007AFF),
                  elevation: 0,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              children: [
                // Table Header
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Parent Role',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Inherits Role',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ), 
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Actions',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Table Row
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Colors.grey[300]!),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          roles[0].roleInheritance.parentRole,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          roles[0].roleInheritance.inheritsRole,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ), 
                      Expanded(
                        flex: 1,
                        child: Row(
                          children: [
                            InkWell(
                              onTap: () {},
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  Icons.edit_outlined,
                                  size: 16,
                                  color: Colors.blue[600],
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            InkWell(
                              onTap: () => _showEditRoleModal(context),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  Icons.delete_outline,
                                  size: 16,
                                  color: Colors.red[600],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }





  // add role modal popup
    void _showAddInheritanceModal(BuildContext context) {
  
    _selectedDepartmentHead = 'Engineering Manager';
    _selectedParentDepartment = 'Employee';
    List<InheritsCreateModel> inheritsModel=[];

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, buildState) {
            return Dialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Container(
                width: 780,
                constraints: const BoxConstraints(maxHeight: 350),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x1A000000), // Black with 10% opacity
                      blurRadius: 16,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                     Container(
                          padding: const EdgeInsets.all(24),
                          decoration: const BoxDecoration(
                            border: Border(
                              bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  "Add Inheritance",
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                    ResponsiveFontSizes.bodyLarge(context),
                                    fontWeight: FontWeight.w600,
                                    fontFamily: FontManager.fontFamilyInter,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                              Row(
                                children: [
                                  // FIXED: Only fixed the functionality, kept your styling
                                  ElevatedButton(
                                    onPressed: 
                                    _selectedParentDepartment.isEmpty||_selectedDepartmentHead.isEmpty?null: () {

                                      inheritsModel.add(InheritsCreateModel(
                                        inheritsRole: _selectedParentDepartment,
                                         parentRole: _selectedDepartmentHead,
                                      
                                      ));
                                      buildState(() {
                                     
                                        // selectedValues will reset automatically
                                      });
                                    },
                                      
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                    _selectedParentDepartment.isEmpty||_selectedParentDepartment.isEmpty ? Colors.grey : Colors.deepPurple,
                                    ),
                                    child: Text('Add More'),
                                  ),
                                  const SizedBox(width: 8),
                                  IconButton(
                                    onPressed: () => Navigator.of(context).pop(),
                                    icon: const Icon(Icons.close),
                                    iconSize: 24,
                                    color: Colors.grey[600],
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            flex: 7,
                            child: Padding(
                              padding: const EdgeInsets.only(left: 32,right: 32,top: 16),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                               
                              
                                  // Department Head Role and Parent Department Row
                                  Row(
                                    children: [
                                      // Department Head Role
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Parent Role',
                                              style: FontManager.getCustomStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w500,
                                                fontFamily: FontManager.fontFamilyTiemposText,
                                                color: Colors.black,
                                              ),
                                            ),
                                            Container(
                                              height: 44,
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                    color: const Color(0xFFE5E7EB)),
                                                borderRadius: BorderRadius.circular(6),
                                                color: Colors.white,
                                              ),
                                              child: DropdownButtonFormField<String>(
                                                value: _selectedDepartmentHead,
                                                decoration: const InputDecoration(
                                                  border: InputBorder.none,
                                                  contentPadding: EdgeInsets.symmetric(
                                                    horizontal: 16,
                                                    vertical: 12,
                                                  ),
                                                ),
                                                style: FontManager.getCustomStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily:
                                                      FontManager.fontFamilyTiemposText,
                                                  color: Colors.black,
                                                ),
                                                icon: const Icon(
                                                  Icons.keyboard_arrow_down,
                                                  color: Color(0xFF6B7280),
                                                  size: 20,
                                                ),
                                                items:  [
                                                  'Engineering Manager', // Fixed typo only
                                                  'Engineering Director',
                                                  'CTO',
                                                  'Sales Manager',
                                                  'Sales Director',
                                                  'CEO',
                                                  'Product Director',
                                                  'HR Manager',
                                                  'HR Director',
                                                  'Board of Directors',
                                                ].map((String value) {
                                                  return DropdownMenuItem<String>(
                                                    value: value,
                                                    child: Text(value),
                                                  );
                                                }).toList(),
                                                onChanged: (String? newValue) {
                                                  buildState(() {
                                                    _selectedDepartmentHead = newValue!;
                                                  });
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      // Parent Department
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Inherits Role',
                                              style: FontManager.getCustomStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w500,
                                                fontFamily: FontManager.fontFamilyTiemposText,
                                                color: Colors.black,
                                              ),
                                            ),
                                            const SizedBox(height: 8),
                                            Container(
                                              height: 44,
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                    color: const Color(0xFFE5E7EB)),
                                                borderRadius: BorderRadius.circular(6),
                                                color: Colors.white,
                                              ),
                                              child: DropdownButtonFormField<String>(
                                                value: _selectedParentDepartment,
                                                decoration: const InputDecoration(
                                                  border: InputBorder.none,
                                                  contentPadding: EdgeInsets.symmetric(
                                                    horizontal: 16,
                                                    vertical: 12,
                                                  ),
                                                ),
                                                style: FontManager.getCustomStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily:
                                                      FontManager.fontFamilyTiemposText,
                                                  color: Colors.black,
                                                ),
                                                icon: const Icon(
                                                  Icons.keyboard_arrow_down,
                                                  color: Color(0xFF6B7280),
                                                  size: 20,
                                                ),
                                                items:['Employee',
                                                  'Manager',
                                                  'Director',
                                                  'Executive',
                                                ].map((String value) {
                                                  return DropdownMenuItem<String>(
                                                    value: value,
                                                    child: Text(value),
                                                  );
                                                }).toList(),
                                                onChanged: (String? newValue) {
                                                  buildState(() {
                                                    _selectedParentDepartment = newValue!;
                                                  });
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  
                                 
                                
                                ],
                              ),
                            ),
                          ),
                          Expanded(
                                      flex: 3,
                                      child:  Container(
                                    decoration: const BoxDecoration(
                                      border: Border(
                                        left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                      ),
                                      color: Colors.white,
                                    ),
                                    child: inheritsModel.isEmpty
                                        ? Center(child: Text("No departments added yet", style: TextStyle(color: Colors.grey)))
                                        :
                                        ListView.builder(
                                          padding: const EdgeInsets.all(16),
                                          itemCount: inheritsModel.length,
                                          itemBuilder: (context, index) {
                                            final role = inheritsModel[index];
                                            return Padding(
                                              padding: const EdgeInsets.symmetric(vertical: 8),
                                              child: Row(
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  // Index number
                                                  Text(
                                                    '${index + 1}. ',
                                                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                                  ),
                                              
                                                  // Role name expands to take available space
                                                  Expanded(
                                                    child: Text(
                                                      role.parentRole,
                                                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                                                    ),
                                                  ),
                                              
                                                  // Edit and Delete buttons aligned right
                                                  IconButton(
                                                    icon: const Icon(Icons.edit, color: Colors.blue),
                                                    iconSize: 20,
                                                    onPressed: () {
                                                      // Handle edit
                                                    },
                                                  ),
                                                  IconButton(
                                                    icon: const Icon(Icons.delete, color: Colors.red),
                                                    iconSize: 20,
                                                    onPressed: () {
                                                      // buildState(() {
                                                      //   roleList.removeAt(index);
                                                      // });
                                                    },
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                        )
                                      ),
                                    )
                        ],
                      ),
                    ),
                          SizedBox(height: 10,),
                              // Action Buttons
                               Container(
                          padding: const EdgeInsets.all(24),
                          decoration: const BoxDecoration(
                            border: Border(
                              top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                            ),
                          ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    OutlinedButton(
                                      onPressed: () => Navigator.of(context).pop(),
                                      style: OutlinedButton.styleFrom(
                                        side: const BorderSide(color: Color(0xFFD1D5DB)),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(6),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 20,
                                          vertical: 10,
                                        ),
                                        backgroundColor: Colors.white,
                                      ),
                                      child: Text(
                                        'Cancel',
                                        style: FontManager.getCustomStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: FontManager.fontFamilyTiemposText,
                                          color: const Color(0xFF374151),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    ElevatedButton(
                                      onPressed: () async{
                                        // Handle form submission here
                                        final provider =
                                Provider.of<ObjectCreationProvider>(context, listen: false);
                                  // For Validate button, just close the dialog
                                await  provider.parseValidateInheritanceEntity(inheritsModel);
                                        // Navigator.of(context).pop();
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: const Color(0xFF3B82F6),
                                        elevation: 0,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(6),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 20,
                                          vertical: 10,
                                        ),
                                      ),
                                      child: Text(
                                        'Apply This',
                                        style: FontManager.getCustomStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: FontManager.fontFamilyTiemposText,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  
  }
// add role modal popup
    void _showAddDepartmentModal(BuildContext context) {
    // Reset form fields
    _departmentNameController.text = '';
    _descriptionController.text =
        '';
    _selectedDepartmentHead = 'Engineering Manager';
    _selectedParentDepartment = 'Engineering';
    List<DeptCreateModel> deptCreateModel=[];

       

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, buildState) {
            return Dialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Container(
                width: 780,
                constraints: const BoxConstraints(maxHeight: 500),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x1A000000), // Black with 10% opacity
                      blurRadius: 16,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                     Container(
                          padding: const EdgeInsets.all(24),
                          decoration: const BoxDecoration(
                            border: Border(
                              bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  "Add Department",
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                    ResponsiveFontSizes.bodyLarge(context),
                                    fontWeight: FontWeight.w600,
                                    fontFamily: FontManager.fontFamilyInter,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                              Row(
                                children: [
                                  // FIXED: Only fixed the functionality, kept your styling
                                  ElevatedButton(
                                    onPressed: _descriptionController.text.isEmpty||_departmentNameController.text.isEmpty||
                                    _selectedParentDepartment.isEmpty||_selectedDepartmentHead.isEmpty?null: () {
                                      log("0000000000000");
                                      deptCreateModel.add(DeptCreateModel(
                                        department: _selectedParentDepartment,
                                         deptHeadRole: _selectedDepartmentHead,
                                         deptName: _departmentNameController.text,
                                         description: _descriptionController.text,
                                      ));
                                      buildState(() {
                                        _descriptionController.clear();
                                        _departmentNameController.clear();
                                        // selectedValues will reset automatically
                                      });
                                    },
                                      
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: _descriptionController.text.isEmpty||_departmentNameController.text.isEmpty||
                                    _selectedParentDepartment.isEmpty||_selectedParentDepartment.isEmpty ? Colors.grey : Colors.deepPurple,
                                    ),
                                    child: Text('Add More'),
                                  ),
                                  const SizedBox(width: 8),
                                  IconButton(
                                    onPressed: () => Navigator.of(context).pop(),
                                    icon: const Icon(Icons.close),
                                    iconSize: 24,
                                    color: Colors.grey[600],
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            flex: 7,
                            child: Padding(
                              padding: const EdgeInsets.only(left: 32,right: 32,top: 16),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                               
                              
                                  // Department Name Field
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Department Name',
                                        style: FontManager.getCustomStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: FontManager.fontFamilyTiemposText,
                                          color: Colors.black,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Container(
                                        height: 44,
                                        decoration: BoxDecoration(
                                          border: Border.all(color: const Color(0xFFE5E7EB)),
                                          borderRadius: BorderRadius.circular(6),
                                          color: Colors.white,
                                        ),
                                        child: TextField(
                                          controller: _departmentNameController,
                                          decoration: InputDecoration(
                                            border: InputBorder.none,
                                            //enabledBorder: InputBorder.none,
                                            contentPadding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 12,
                                            ),
                                            hintText: 'CEO',
                                            hintStyle: FontManager.getCustomStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                              fontFamily: FontManager.fontFamilyTiemposText,
                                              color: const Color(0xFF9CA3AF),
                                            ),
                                          ),
                                          style: FontManager.getCustomStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: FontManager.fontFamilyTiemposText,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                              
                                  // Description Field
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Description',
                                        style: FontManager.getCustomStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: FontManager.fontFamilyTiemposText,
                                          color: Colors.black,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Container(
                                        height: 80,
                                        decoration: BoxDecoration(
                                          border: Border.all(color: const Color(0xFFE5E7EB)),
                                          borderRadius: BorderRadius.circular(6),
                                          color: Colors.white,
                                        ),
                                        child: TextField(
                                          controller: _descriptionController,
                                          maxLines: 3,
                                          decoration: InputDecoration(
                                            border: InputBorder.none,
                                            contentPadding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 12,
                                            ),
                                            hintText: 'Enter department description',
                                            hintStyle: FontManager.getCustomStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                              fontFamily: FontManager.fontFamilyTiemposText,
                                              color: const Color(0xFF9CA3AF),
                                            ),
                                          ),
                                          style: FontManager.getCustomStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: FontManager.fontFamilyTiemposText,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                              
                                  // Department Head Role and Parent Department Row
                                  Row(
                                    children: [
                                      // Department Head Role
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Department Head Role',
                                              style: FontManager.getCustomStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w500,
                                                fontFamily: FontManager.fontFamilyTiemposText,
                                                color: Colors.black,
                                              ),
                                            ),
                                            Container(
                                              height: 44,
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                    color: const Color(0xFFE5E7EB)),
                                                borderRadius: BorderRadius.circular(6),
                                                color: Colors.white,
                                              ),
                                              child: DropdownButtonFormField<String>(
                                                value: _selectedDepartmentHead,
                                                decoration: const InputDecoration(
                                                  border: InputBorder.none,
                                                  contentPadding: EdgeInsets.symmetric(
                                                    horizontal: 16,
                                                    vertical: 12,
                                                  ),
                                                ),
                                                style: FontManager.getCustomStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily:
                                                      FontManager.fontFamilyTiemposText,
                                                  color: Colors.black,
                                                ),
                                                icon: const Icon(
                                                  Icons.keyboard_arrow_down,
                                                  color: Color(0xFF6B7280),
                                                  size: 20,
                                                ),
                                                items:  [
                                                  'Engineering Manager', // Fixed typo only
                                                  'Engineering Director',
                                                  'CTO',
                                                  'Sales Manager',
                                                  'Sales Director',
                                                  'CEO',
                                                  'Product Director',
                                                  'HR Manager',
                                                  'HR Director',
                                                  'Board of Directors',
                                                ].map((String value) {
                                                  return DropdownMenuItem<String>(
                                                    value: value,
                                                    child: Text(value),
                                                  );
                                                }).toList(),
                                                onChanged: (String? newValue) {
                                                  buildState(() {
                                                    _selectedDepartmentHead = newValue!;
                                                  });
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      // Parent Department
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Parent Department',
                                              style: FontManager.getCustomStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w500,
                                                fontFamily: FontManager.fontFamilyTiemposText,
                                                color: Colors.black,
                                              ),
                                            ),
                                            const SizedBox(height: 8),
                                            Container(
                                              height: 44,
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                    color: const Color(0xFFE5E7EB)),
                                                borderRadius: BorderRadius.circular(6),
                                                color: Colors.white,
                                              ),
                                              child: DropdownButtonFormField<String>(
                                                value: _selectedParentDepartment,
                                                decoration: const InputDecoration(
                                                  border: InputBorder.none,
                                                  contentPadding: EdgeInsets.symmetric(
                                                    horizontal: 16,
                                                    vertical: 12,
                                                  ),
                                                ),
                                                style: FontManager.getCustomStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily:
                                                      FontManager.fontFamilyTiemposText,
                                                  color: Colors.black,
                                                ),
                                                icon: const Icon(
                                                  Icons.keyboard_arrow_down,
                                                  color: Color(0xFF6B7280),
                                                  size: 20,
                                                ),
                                                items:['Engineering',
                                                  'Sales',
                                                  'Product',
                                                  'Executive',
                                                  'Technology',
                                                  'Finance',
                                                  'Human Resources',
                                                ].map((String value) {
                                                  return DropdownMenuItem<String>(
                                                    value: value,
                                                    child: Text(value),
                                                  );
                                                }).toList(),
                                                onChanged: (String? newValue) {
                                                  buildState(() {
                                                    _selectedParentDepartment = newValue!;
                                                  });
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  
                                 
                                
                                ],
                              ),
                            ),
                          ),
                          Expanded(
                                      flex: 3,
                                      child:  Container(
                                    decoration: const BoxDecoration(
                                      border: Border(
                                        left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                      ),
                                      color: Colors.white,
                                    ),
                                    child: deptCreateModel.isEmpty
                                        ? Center(child: Text("No departments added yet", style: TextStyle(color: Colors.grey)))
                                        :
                                        ListView.builder(
                                          padding: const EdgeInsets.all(16),
                                          itemCount: deptCreateModel.length,
                                          itemBuilder: (context, index) {
                                            final role = deptCreateModel[index];
                                            return Padding(
                                              padding: const EdgeInsets.symmetric(vertical: 8),
                                              child: Row(
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  // Index number
                                                  Text(
                                                    '${index + 1}. ',
                                                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                                  ),
                                              
                                                  // Role name expands to take available space
                                                  Expanded(
                                                    child: Text(
                                                      role.deptName,
                                                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                                                    ),
                                                  ),
                                              
                                                  // Edit and Delete buttons aligned right
                                                  IconButton(
                                                    icon: const Icon(Icons.edit, color: Colors.blue),
                                                    iconSize: 20,
                                                    onPressed: () {
                                                      // Handle edit
                                                    },
                                                  ),
                                                  IconButton(
                                                    icon: const Icon(Icons.delete, color: Colors.red),
                                                    iconSize: 20,
                                                    onPressed: () {
                                                      // buildState(() {
                                                      //   roleList.removeAt(index);
                                                      // });
                                                    },
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                        )
                                      ),
                                    )
                        ],
                      ),
                    ),
                          SizedBox(height: 10,),
                              // Action Buttons
                               Container(
                          padding: const EdgeInsets.all(24),
                          decoration: const BoxDecoration(
                            border: Border(
                              top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                            ),
                          ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    OutlinedButton(
                                      onPressed: () => Navigator.of(context).pop(),
                                      style: OutlinedButton.styleFrom(
                                        side: const BorderSide(color: Color(0xFFD1D5DB)),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(6),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 20,
                                          vertical: 10,
                                        ),
                                        backgroundColor: Colors.white,
                                      ),
                                      child: Text(
                                        'Cancel',
                                        style: FontManager.getCustomStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: FontManager.fontFamilyTiemposText,
                                          color: const Color(0xFF374151),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    ElevatedButton(
                                      onPressed: () async{
                                        // Handle form submission here
                                        final provider =
                                Provider.of<ObjectCreationProvider>(context, listen: false);
                                  // For Validate button, just close the dialog
                                await  provider.parseValidateDeptEntity(deptCreateModel);
                                        // Navigator.of(context).pop();
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: const Color(0xFF3B82F6),
                                        elevation: 0,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(6),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 20,
                                          vertical: 10,
                                        ),
                                      ),
                                      child: Text(
                                        'Apply This',
                                        style: FontManager.getCustomStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: FontManager.fontFamilyTiemposText,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

// EDIT role modal popup
    void _showEditRoleModal(BuildContext context) {
    // Reset form fields
    _departmentNameController.text = 'Sales';
    _descriptionController.text =
        'Chief Executive Officer, overall company leadership and strategy';
    _selectedDepartmentHead = 'VP Marketing';
    _selectedParentDepartment = 'None';

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Container(
                width: 520,
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x1A000000), // Black with 10% opacity
                      blurRadius: 16,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Edit Role',
                          style: FontManager.getCustomStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                        InkWell(
                          onTap: () => Navigator.of(context).pop(),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            child: const Icon(
                              Icons.close,
                              size: 24,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),

                    // Department Name Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Role Name',
                          style: FontManager.getCustomStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          height: 44,
                          decoration: BoxDecoration(
                            border: Border.all(color: const Color(0xFFE5E7EB)),
                            borderRadius: BorderRadius.circular(6),
                            color: Colors.white,
                          ),
                          child: TextField(
                            controller: _departmentNameController,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              //enabledBorder: InputBorder.none,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              hintText: 'CEO',
                              hintStyle: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: const Color(0xFF9CA3AF),
                              ),
                            ),
                            style: FontManager.getCustomStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Description Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Description',
                          style: FontManager.getCustomStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          height: 80,
                          decoration: BoxDecoration(
                            border: Border.all(color: const Color(0xFFE5E7EB)),
                            borderRadius: BorderRadius.circular(6),
                            color: Colors.white,
                          ),
                          child: TextField(
                            controller: _descriptionController,
                            maxLines: 3,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              hintText: 'Enter department description',
                              hintStyle: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: const Color(0xFF9CA3AF),
                              ),
                            ),
                            style: FontManager.getCustomStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Department Head Role and Parent Department Row
                    Row(
                      children: [
                        // Department Head Role
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Reports To',
                                style: FontManager.getCustomStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                height: 44,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: const Color(0xFFE5E7EB)),
                                  borderRadius: BorderRadius.circular(6),
                                  color: Colors.white,
                                ),
                                child: DropdownButtonFormField<String>(
                                  value: _selectedDepartmentHead,
                                  decoration: const InputDecoration(
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                  ),
                                  style: FontManager.getCustomStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                  icon: const Icon(
                                    Icons.keyboard_arrow_down,
                                    color: Color(0xFF6B7280),
                                    size: 20,
                                  ),
                                  items: [
                                    'VP Marketing',
                                    'CEO',
                                    'CTO',
                                    'CFO',
                                    'Manager'
                                  ].map((String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(value),
                                    );
                                  }).toList(),
                                  onChanged: (String? newValue) {
                                    setState(() {
                                      _selectedDepartmentHead = newValue!;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        // Parent Department
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Organization Level',
                                style: FontManager.getCustomStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                height: 44,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: const Color(0xFFE5E7EB)),
                                  borderRadius: BorderRadius.circular(6),
                                  color: Colors.white,
                                ),
                                child: DropdownButtonFormField<String>(
                                  value: _selectedParentDepartment,
                                  decoration: const InputDecoration(
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                  ),
                                  style: FontManager.getCustomStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                  icon: const Icon(
                                    Icons.keyboard_arrow_down,
                                    color: Color(0xFF6B7280),
                                    size: 20,
                                  ),
                                  items: [
                                    'None',
                                    'Executive',
                                    'Sales',
                                    'Marketing',
                                    'Engineering'
                                  ].map((String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(value),
                                    );
                                  }).toList(),
                                  onChanged: (String? newValue) {
                                    setState(() {
                                      _selectedParentDepartment = newValue!;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),

                    // Department Head Role and Parent Department Row
                    Row(
                      children: [
                        // Department Head Role
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Department',
                                style: FontManager.getCustomStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                height: 44,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: const Color(0xFFE5E7EB)),
                                  borderRadius: BorderRadius.circular(6),
                                  color: Colors.white,
                                ),
                                child: DropdownButtonFormField<String>(
                                  value: _selectedDepartmentHead,
                                  decoration: const InputDecoration(
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                  ),
                                  style: FontManager.getCustomStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                  icon: const Icon(
                                    Icons.keyboard_arrow_down,
                                    color: Color(0xFF6B7280),
                                    size: 20,
                                  ),
                                  items: [
                                    'VP Marketing',
                                    'CEO',
                                    'CTO',
                                    'CFO',
                                    'Manager'
                                  ].map((String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(value),
                                    );
                                  }).toList(),
                                  onChanged: (String? newValue) {
                                    setState(() {
                                      _selectedDepartmentHead = newValue!;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        // Parent Department
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Inherits',
                                style: FontManager.getCustomStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                height: 44,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: const Color(0xFFE5E7EB)),
                                  borderRadius: BorderRadius.circular(6),
                                  color: Colors.white,
                                ),
                                child: DropdownButtonFormField<String>(
                                  value: _selectedParentDepartment,
                                  decoration: const InputDecoration(
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                  ),
                                  style: FontManager.getCustomStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                  icon: const Icon(
                                    Icons.keyboard_arrow_down,
                                    color: Color(0xFF6B7280),
                                    size: 20,
                                  ),
                                  items: [
                                    'None',
                                    'Executive',
                                    'Sales',
                                    'Marketing',
                                    'Engineering'
                                  ].map((String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(value),
                                    );
                                  }).toList(),
                                  onChanged: (String? newValue) {
                                    setState(() {
                                      _selectedParentDepartment = newValue!;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 40),

                    // Action Buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Container(
                          height: 40,
                          child: OutlinedButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: OutlinedButton.styleFrom(
                              side: const BorderSide(color: Color(0xFFD1D5DB)),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 10,
                              ),
                              backgroundColor: Colors.white,
                            ),
                            child: Text(
                              'Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: const Color(0xFF374151),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Container(
                          height: 40,
                          child: ElevatedButton(
                            onPressed: () {
                              // Handle form submission here
                              _handleAddDepartment();
                              Navigator.of(context).pop();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF3B82F6),
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 10,
                              ),
                            ),
                            child: Text(
                              'Apply This',
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }


  void _showAddRoleModal(BuildContext context) {
    // Move these OUTSIDE StatefulBuilder to maintain state - MINIMAL CHANGE
    final TextEditingController roleNameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    List<RoleCreateModel> roleList = [];
        String? selectedReportsTo = '';
            String? selectedDepartment = '';
            String? selectedOrgLevel = '';
            String? selectedInherits = '';

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, buildState) {
        

            // FIXED: Only fixed the validation logic
            bool isFormValid() {
              return roleNameController.text.isNotEmpty &&
                  descriptionController.text.isNotEmpty &&
                  selectedReportsTo != null && selectedReportsTo!.isNotEmpty &&
                  selectedDepartment != null && selectedDepartment!.isNotEmpty &&
                  selectedOrgLevel != null && selectedOrgLevel!.isNotEmpty &&
                  selectedInherits != null && selectedInherits!.isNotEmpty;
            }

            return Consumer<ObjectCreationProvider>(
              builder: (context,data,_) {
                return Dialog(
                  backgroundColor: Colors.transparent,
                  child: Container(
                    width: 780,
                    constraints: const BoxConstraints(maxHeight: 500),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Header - KEPT YOUR ORIGINAL DESIGN
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: const BoxDecoration(
                            border: Border(
                              bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  "Add Role",
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                    ResponsiveFontSizes.bodyLarge(context),
                                    fontWeight: FontWeight.w600,
                                    fontFamily: FontManager.fontFamilyInter,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                              Row(
                                children: [
                                  // FIXED: Only fixed the functionality, kept your styling
                                  ElevatedButton(
                                    onPressed: roleNameController.text.isNotEmpty
                                        ? () {
                                          log("000000000000       $selectedReportsTo");
                                      roleList.add(RoleCreateModel(
                                        roleName: roleNameController.text,
                                        description: descriptionController.text,
                                        reportsTo: selectedReportsTo!,
                                        department: selectedDepartment!,
                                        orgLevel: selectedOrgLevel!,
                                        inherits: selectedInherits!,
                                      ));
                                      buildState(() {
                                        roleNameController.clear();
                                        descriptionController.clear();
                                        // selectedValues will reset automatically
                                      });
                                    }
                                        : null,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: isFormValid() ? Colors.deepPurple : Colors.deepPurple,
                                    ),
                                    child: Text('Add More'),
                                  ),
                                  const SizedBox(width: 8),
                                  IconButton(
                                    onPressed: () => Navigator.of(context).pop(),
                                    icon: const Icon(Icons.close),
                                    iconSize: 24,
                                    color: Colors.grey[600],
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Row(
                            children: [
                              // Left side - Form (70% always) - KEPT YOUR ORIGINAL
                              Expanded(
                                flex: 7,
                                child: SingleChildScrollView(
                                  padding: const EdgeInsets.all(24),
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // Left column
                                      Expanded(
                                        child: Column(
                                          children: [
                                            _buildEditableFormField(
                                                context,
                                                'Role Name',
                                                roleNameController),
                                            const SizedBox(height: 20),
                                            _buildEditableDropdownField(
                                                context,
                                                'Reports To',
                                                [
                                                  'Engineering Manager', // Fixed typo only
                                                  'Engineering Director',
                                                  'CTO',
                                                  'Sales Manager',
                                                  'Sales Director',
                                                  'CEO',
                                                  'Product Director',
                                                  'HR Manager',
                                                  'HR Director',
                                                  'Board of Directors',
                                                ],
                                                selectedReportsTo!, (value) {
                                              buildState(() {
                                                selectedReportsTo = value!;
                                                log(selectedReportsTo.toString());
                                              });
                                            }
                                            ),
                                            const SizedBox(height: 20),
                                            _buildEditableDropdownField(
                                                context,
                                                'Organization Level',
                                                ['Executive',
                                                  'Director',
                                                  'Management',
                                                  'Team',
                                                  'Individual',
                                                ],
                                                selectedOrgLevel!, (value) {
                                              buildState(() {
                                                selectedOrgLevel = value!;
                                              });
                                            }),
                                            const SizedBox(height: 20),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 24),
                                      // Right column
                                      Expanded(
                                        child: Column(
                                          children: [
                                            _buildEditableFormField(
                                                context,
                                                'Description',
                                                descriptionController),
                                            const SizedBox(height: 20),
                                            _buildEditableDropdownField(
                                                context,
                                                'Department',
                                                ['Engineering',
                                                  'Sales',
                                                  'Product',
                                                  'Executive',
                                                  'Technology',
                                                  'Finance',
                                                  'Human Resources',
                                                ],
                                                selectedDepartment!, (value) {
                                              buildState(() {
                                                selectedDepartment = value!;
                                              });
                                            }),
                                            const SizedBox(height: 20),
                                            _buildEditableDropdownField(
                                                context,
                                                'Inherits',
                                                ['Employee',
                                                  'Manager',
                                                  'Director',
                                                  'Executive',
                                                ],
                                                selectedInherits!, (value) {
                                              buildState(() {
                                                selectedInherits = value!;
                                              });
                                            }),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              // Right side - Always show (30% space) - KEPT YOUR ORIGINAL DESIGN
                              Expanded(
                                flex: 3,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    border: Border(
                                      left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                    ),
                                    color: Colors.white,
                                  ),
                                  child: roleList.isEmpty
                                      ? Center(child: Text("No roles added yet", style: TextStyle(color: Colors.grey)))
                                      :
                                  ListView.builder(
                                    padding: const EdgeInsets.all(16),
                                    itemCount: roleList.length,
                                    itemBuilder: (context, index) {
                                      final role = roleList[index];
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 8),
                                        child: Row(
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            // Index number
                                            Text(
                                              '${index + 1}. ',
                                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                            ),
                
                                            // Role name expands to take available space
                                            Expanded(
                                              child: Text(
                                                role.roleName,
                                                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                                              ),
                                            ),
                
                                            // Edit and Delete buttons aligned right
                                            IconButton(
                                              icon: const Icon(Icons.edit, color: Colors.blue),
                                              iconSize: 20,
                                              onPressed: () {
                                                // Handle edit
                                              },
                                            ),
                                            IconButton(
                                              icon: const Icon(Icons.delete, color: Colors.red),
                                              iconSize: 20,
                                              onPressed: () {
                                                buildState(() {
                                                  roleList.removeAt(index);
                                                });
                                              },
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  )
                                ),
                              ),
                            ],
                          ),
                        ),
                        // KEPT YOUR ORIGINAL FOOTER DESIGN
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: const BoxDecoration(
                            border: Border(
                              top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: Text(
                                  'Cancel',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                    fontWeight: FontWeight.w500,
                                    fontFamily: FontManager.fontFamilyTiemposText,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              ElevatedButton(
                                onPressed: () async{
                                   final provider =
                                Provider.of<ObjectCreationProvider>(context, listen: false);
                                  // For Validate button, just close the dialog
                                await  provider.parseValidateAddRoleEntity(roleList);
                                  if(data.saveRoleModel?.success??false){
                                     for(var  param in roleList){
                                    log(param.department);
                                     log(param.reportsTo);
                                      log(param.orgLevel);
                                       log(param.inherits);
                                        log(param.description);
                                        roles.add(Role(roleName: param.roleName, roleConfiguration: RoleConfiguration(roleName: param.roleName, description: param.description, reportsTo: param.reportsTo, organizationLevel: param.orgLevel, department: param.department, inherits: param.inherits), departmentConfiguration: DepartmentConfiguration(departmentName: param.department, description: param.description, departmentHeadRole: param.department), roleInheritance: RoleInheritance(parentRole: param.roleName, inheritsRole: param.inherits)));
                                  }
                                    Navigator.of(context).pop();
                                    setState(() {
                                      
                                    },);
                                  }
                
                                
                                  // Navigator.of(context).pop();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF0058FF),
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  elevation: 0,
                                ),
                                child: Text(
                                  'Validate',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                    fontWeight: FontWeight.w500,
                                    fontFamily: FontManager.fontFamilyTiemposText,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }
            );
          },
        );
      },
    );
  }
  Widget _buildEditableFormField(
      BuildContext context, String label, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }


  Widget _buildDropdown(String label, List<String> options, String selectedValue,
      ValueChanged<String?> onChanged) {
    return SizedBox(
      width: 260,
      child: DropdownButtonFormField<String>(
        value: selectedValue,
        decoration: InputDecoration(
          labelText: label,
          border: OutlineInputBorder(),
        ),
        items: options
            .map((value) => DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        ))
            .toList(),
        onChanged: onChanged,
      ),
    );
  }

  void _handleAddDepartment() async{
    // Handle the form submission logic here
    // RoleCreateModel? deptObject;
    log('Department Name: ${_departmentNameController.text}');
    log('Description: ${_descriptionController.text}');
    log('Department Head Role: $_selectedDepartmentHead');
    log('Parent Department: $_selectedParentDepartment');

        

    // You can add your logic here to save the department data
    // For example, add to a list, send to API, etc.
  }

  Widget _buildEditableDropdownField(BuildContext context, String label,
      List<String> options, String selectedValue, Function(String?) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          child: DropdownButtonFormField<String>(
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF0058FF)),
              ),
              contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              filled: true,
              fillColor: Colors.white,
            ),
            icon: Container(
              alignment: Alignment.centerRight,
              child: const Icon(
                Icons.keyboard_arrow_down,
                size: 24,
              ),
            ),
            iconSize: 24,
            isExpanded: true,
            value:
            options.contains(selectedValue) ? selectedValue : options.first,
            items: options.map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Container(
                  width: double.infinity,
                  child: Text(
                    value,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              );
            }).toList(),
            onChanged: onChanged,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
        ),
      ],
    );
  }}

// Shared HoverBellIcon component
class HoverBellIcon extends StatefulWidget {
  final VoidCallback onTap;

  const HoverBellIcon({
    super.key,
    required this.onTap,
  });

  @override
  State<HoverBellIcon> createState() => _HoverBellIconState();
}

class _HoverBellIconState extends State<HoverBellIcon> {
  bool isHovered = false;
  OverlayEntry? _overlayEntry;
  final GlobalKey _bellIconKey = GlobalKey();
  bool _isOverPopup = false;

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _showOverlay() {
    if (_overlayEntry != null) return;

    final RenderBox? renderBox =
        _bellIconKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx - 280,
        top: position.dy + size.height + 8,
        child: MouseRegion(
          onEnter: (_) {
            _isOverPopup = true;
          },
          onExit: (_) {
            _isOverPopup = false;
            Future.delayed(Duration(milliseconds: 50), () {
              if (mounted && !isHovered && !_isOverPopup) {
                _removeOverlay();
              }
            });
          },
          child: Material(
            color: Colors.transparent,
            child: _buildHoverPopup(),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _handleMouseExit() {
    setState(() => isHovered = false);
    Future.delayed(Duration(milliseconds: 50), () {
      if (mounted && !isHovered && !_isOverPopup) {
        _removeOverlay();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() => isHovered = true);
        _showOverlay();
      },
      onExit: (_) {
        _handleMouseExit();
      },
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Container(
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: Icon(
              key: _bellIconKey,
              Icons.notifications_outlined,
              size: 18,
              color: Color(0xffFF2019),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHoverPopup() {
    return Container(
      width: 300,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'This Objects is already exists in your library. You need to rename the Objects to proceed.',
            textAlign: TextAlign.center,
            style: FontManager.getCustomStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              height: 1.4,
            ),
          ),
          SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                height: 32,
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      isHovered = false;
                      _isOverPopup = false;
                    });
                    _removeOverlay();
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey.shade400, width: 1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    backgroundColor: Colors.white,
                  ),
                  child: Text(
                    'Continue',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: Colors.black87,
                      fontWeight: FontManager.medium,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              SizedBox(
                height: 32,
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      isHovered = false;
                      _isOverPopup = false;
                    });
                    _removeOverlay();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xff007AFF),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    elevation: 0,
                  ),
                  child: Text(
                    'Resolve',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: Colors.white,
                      fontWeight: FontManager.medium,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
 