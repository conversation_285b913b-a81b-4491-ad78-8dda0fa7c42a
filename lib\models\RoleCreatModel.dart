class RoleCreateModel {
  final String roleName;
  final String description;
  final String reportsTo;
  final String department;
  final String orgLevel;
  final String inherits;

  RoleCreateModel({
    required this.roleName,
    required this.description,
    required this.reportsTo,
    required this.department,
    required this.orgLevel,
    required this.inherits,
  });

  RoleCreateModel copyWith({
    String? roleName,
    String? description,
    String? reportsTo,
    String? department,
    String? orgLevel,
    String? inherits,
  }) {
    return RoleCreateModel(
      roleName: roleName ?? this.roleName,
      description: description ?? this.description,
      reportsTo: reportsTo ?? this.reportsTo,
      department: department ?? this.department,
      orgLevel: orgLevel ?? this.orgLevel,
      inherits: inherits ?? this.inherits,
    );
  }
}



class DeptCreateModel {
  final String deptName;
  final String description;
  final String deptHeadRole;
  final String department;

  DeptCreateModel({
    required this.deptName,
    required this.description,
    required this.deptHeadRole,
    required this.department,
  });
}

class InheritsCreateModel {
  final String parentRole;
  final String inheritsRole;

  InheritsCreateModel({
    required this.parentRole,
    required this.inheritsRole,
  });
}

