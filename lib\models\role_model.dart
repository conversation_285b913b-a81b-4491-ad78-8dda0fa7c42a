class RoleModel {
    List<PostgresRole>? postgresRoles;
    List<dynamic>? mongoDrafts;
    int? totalPostgres;
    int? totalDrafts;

    RoleModel({
        this.postgresRoles,
        this.mongoDrafts,
        this.totalPostgres,
        this.totalDrafts,
    });

    factory RoleModel.fromJson(Map<String, dynamic> json) => RoleModel(
        postgresRoles: json["postgres_roles"] == null ? [] : List<PostgresRole>.from(json["postgres_roles"]!.map((x) => PostgresRole.fromJson(x))),
        mongoDrafts: json["mongo_drafts"] == null ? [] : List<dynamic>.from(json["mongo_drafts"]!.map((x) => x)),
        totalPostgres: json["total_postgres"],
        totalDrafts: json["total_drafts"],
    );

    Map<String, dynamic> toJson() => {
        "postgres_roles": postgresRoles == null ? [] : List<dynamic>.from(postgresRoles!.map((x) => x.toJson())),
        "mongo_drafts": mongoDrafts == null ? [] : List<dynamic>.from(mongoDrafts!.map((x) => x)),
        "total_postgres": totalPostgres,
        "total_drafts": totalDrafts,
    };

    RoleModel copyWith({
        List<PostgresRole>? postgresRoles,
        List<dynamic>? mongoDrafts,
        int? totalPostgres,
        int? totalDrafts,
    }) =>
        RoleModel(
            postgresRoles: postgresRoles ?? this.postgresRoles,
            mongoDrafts: mongoDrafts ?? this.mongoDrafts,
            totalPostgres: totalPostgres ?? this.totalPostgres,
            totalDrafts: totalDrafts ?? this.totalDrafts,
        );
}

class PostgresRole {
    String? roleId;
    String? name;
    TenantId? tenantId;
    String? inheritsFrom;
    DateTime? createdAt;
    DateTime? updatedAt;
    String? description;
    dynamic permissions;
    String? scope;
    String? classification;
    String? specialConditions;
    VersionType? versionType;
    int? id;
    String? reportsToRoleId;
    String? organizationalLevel;
    int? departmentId;
    String? naturalLanguage;
    int? version;
    AtedBy? createdBy;
    AtedBy? updatedBy;

    PostgresRole({
        this.roleId,
        this.name,
        this.tenantId,
        this.inheritsFrom,
        this.createdAt,
        this.updatedAt,
        this.description,
        this.permissions,
        this.scope,
        this.classification,
        this.specialConditions,
        this.versionType,
        this.id,
        this.reportsToRoleId,
        this.organizationalLevel,
        this.departmentId,
        this.naturalLanguage,
        this.version,
        this.createdBy,
        this.updatedBy,
    });

    factory PostgresRole.fromJson(Map<String, dynamic> json) => PostgresRole(
        roleId: json["role_id"],
        name: json["name"],
        tenantId: tenantIdValues.map[json["tenant_id"]],
        inheritsFrom: json["inherits_from"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        description: json["description"],
        permissions: json["permissions"],
        scope: json["scope"],
        classification: json["classification"],
        specialConditions: json["special_conditions"],
        versionType: versionTypeValues.map[json["version_type"]],
        id: json["id"],
        reportsToRoleId: json["reports_to_role_id"],
        organizationalLevel: json["organizational_level"],
        departmentId: json["department_id"],
        naturalLanguage: json["natural_language"],
        version: json["version"],
        createdBy: atedByValues.map[json["created_by"]],
        updatedBy: atedByValues.map[json["updated_by"]],
    );

    Map<String, dynamic> toJson() => {
        "role_id": roleId,
        "name": name,
        "tenant_id": tenantIdValues.reverse[tenantId],
        "inherits_from": inheritsFrom,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "description": description,
        "permissions": permissions,
        "scope": scope,
        "classification": classification,
        "special_conditions": specialConditions,
        "version_type": versionTypeValues.reverse[versionType],
        "id": id,
        "reports_to_role_id": reportsToRoleId,
        "organizational_level": organizationalLevel,
        "department_id": departmentId,
        "natural_language": naturalLanguage,
        "version": version,
        "created_by": atedByValues.reverse[createdBy],
        "updated_by": atedByValues.reverse[updatedBy],
    };

    PostgresRole copyWith({
        String? roleId,
        String? name,
        TenantId? tenantId,
        String? inheritsFrom,
        DateTime? createdAt,
        DateTime? updatedAt,
        String? description,
        dynamic permissions,
        String? scope,
        String? classification,
        String? specialConditions,
        VersionType? versionType,
        int? id,
        String? reportsToRoleId,
        String? organizationalLevel,
        int? departmentId,
        String? naturalLanguage,
        int? version,
        AtedBy? createdBy,
        AtedBy? updatedBy,
    }) =>
        PostgresRole(
            roleId: roleId ?? this.roleId,
            name: name ?? this.name,
            tenantId: tenantId ?? this.tenantId,
            inheritsFrom: inheritsFrom ?? this.inheritsFrom,
            createdAt: createdAt ?? this.createdAt,
            updatedAt: updatedAt ?? this.updatedAt,
            description: description ?? this.description,
            permissions: permissions ?? this.permissions,
            scope: scope ?? this.scope,
            classification: classification ?? this.classification,
            specialConditions: specialConditions ?? this.specialConditions,
            versionType: versionType ?? this.versionType,
            id: id ?? this.id,
            reportsToRoleId: reportsToRoleId ?? this.reportsToRoleId,
            organizationalLevel: organizationalLevel ?? this.organizationalLevel,
            departmentId: departmentId ?? this.departmentId,
            naturalLanguage: naturalLanguage ?? this.naturalLanguage,
            version: version ?? this.version,
            createdBy: createdBy ?? this.createdBy,
            updatedBy: updatedBy ?? this.updatedBy,
        );
}

enum AtedBy {
    ADMIN,
    API_TEST,
    API_USER
}

enum TenantId {
    T001,
    T002,
    T1001,
    T1008
}

enum VersionType {
    V2
}

final atedByValues = EnumValues({
    "ADMIN": AtedBy.ADMIN,
    "API_TEST": AtedBy.API_TEST,
    "API_USER": AtedBy.API_USER
});

final tenantIdValues = EnumValues({
    "t001": TenantId.T001,
    "t002": TenantId.T002,
    "t1001": TenantId.T1001,
    "t1008": TenantId.T1008
});

final versionTypeValues = EnumValues({
    "v2": VersionType.V2
});

class EnumValues<T> {
    Map<String, T> map;
    late Map<T, String> reverseMap;

    EnumValues(this.map);

    Map<T, String> get reverse {
        reverseMap = map.map((k, v) => MapEntry(v, k));
        return reverseMap;
    }
}
