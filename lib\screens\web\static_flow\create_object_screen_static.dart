import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_tab_header.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/services/multimedia_service.dart';

import 'package:nsl/models/chat_message.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_message_bubble.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class CreateObjectScreenStatic extends StatefulWidget {
  const CreateObjectScreenStatic({super.key});

  @override
  State<CreateObjectScreenStatic> createState() =>
      _CreateObjectScreenStaticState();
}

class _CreateObjectScreenStaticState extends State<CreateObjectScreenStatic> {
  late TextEditingController chatController;
  late MultimediaService _multimediaService;
  bool isLoading = false;
  List<ChatMessage> get messages =>
      Provider.of<WebHomeProviderStatic>(context, listen: false).messages;
  final ScrollController _chatScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    chatController = TextEditingController();
    _multimediaService = MultimediaService();
  }

  @override
  void dispose() {
    chatController.dispose();
    _chatScrollController.dispose();
    super.dispose();
  }

  void _sendMessage() async {
    if (chatController.text.trim().isNotEmpty) {
      final userIntent = chatController.text.trim();
      Logger.info(
          'Starting entity extraction workflow with user intent: $userIntent');

      // Add user message to the list
      setState(() {
        messages.add(ChatMessage(
          content: userIntent,
          isUser: true,
        ));
        isLoading = true;
      });

      // Clear the chat field immediately after adding the message
      chatController.clear();

      // Scroll to bottom to show the new message
      _scrollToBottom();

      try {
        final objectCreationProvider =
            Provider.of<ObjectCreationProvider>(context, listen: false);

        // Execute the complete extraction workflow with user's input
        final success =
            await objectCreationProvider.executeCompleteExtractionWorkflow(
          userIntent: userIntent,
        );

        if (success) {
          setState(() {
            isLoading = false;
          });

          Logger.info(
              'Successfully extracted ${objectCreationProvider.objects.length} objects');

          // Navigate to the next screen
          if (mounted) {
            Provider.of<WebHomeProvider>(context, listen: false)
                .currentScreenIndex = ScreenConstants.aiObjectScreenStatic;
          }
        } else {
          setState(() {
            isLoading = false;
          });

          // Show error popup for API failures
          _showErrorPopup(
              objectCreationProvider.error ?? 'Failed to extract entities');
          Logger.error(
              'Entity extraction failed: ${objectCreationProvider.error}');
        }
      } catch (e) {
        setState(() {
          isLoading = false;
        });

        // Show error popup for exceptions
        String userFriendlyMessage = 'Error during entity extraction: $e';
        if (e is DioException) {
          // Use the enhanced error message from DioClient
          userFriendlyMessage =
              e.message ?? 'An unexpected error occurred. Please try again.';
        }
        _showErrorPopup(userFriendlyMessage);
        Logger.error('Exception during entity extraction: $e');
      }
    }
  }

  // Helper method to scroll to bottom of chat
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_chatScrollController.hasClients) {
        _chatScrollController.animateTo(
          _chatScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _cancelRequest() {
    // Cancel the ongoing API workflow
    Logger.info('Cancelling entity extraction workflow');
    setState(() {
      isLoading = false;
    });
  }

  void _handleFileSelection(String fileName, String filePath) {
    // Handle file selection logic here
    Logger.info('File selected: $fileName at $filePath');
  }

  void _toggleRecording() {
    // Handle recording toggle logic here
    Logger.info('Toggle recording');
  }

  void _showErrorPopup(String errorMessage) {
    // Parse validation errors if it's a JSON response
    String displayMessage = errorMessage;
    String title = 'Error';

    try {
      // Try to parse JSON error response
      if (errorMessage.contains('"error"') &&
          errorMessage.contains('"details"')) {
        // This looks like a validation error JSON
        final RegExp errorRegex = RegExp(r'"error":\s*"([^"]*)"');
        final RegExp messageRegex = RegExp(r'"message":\s*"([^"]*)"');
        final RegExp msgRegex = RegExp(r'"msg":\s*"([^"]*)"');

        final errorMatch = errorRegex.firstMatch(errorMessage);
        final messageMatch = messageRegex.firstMatch(errorMessage);
        final msgMatch = msgRegex.firstMatch(errorMessage);

        if (errorMatch != null) {
          title = errorMatch.group(1) ?? 'Validation Error';
        }

        if (messageMatch != null) {
          displayMessage = messageMatch.group(1) ?? errorMessage;
        } else if (msgMatch != null) {
          displayMessage = msgMatch.group(1) ?? errorMessage;
        }

        // Extract specific validation messages
        if (errorMessage.contains('String should have at least')) {
          displayMessage =
              'Please provide a more detailed description (at least 10 characters)';
        }
      }
    } catch (e) {
      // If parsing fails, use the original message
      Logger.error('Error parsing error message: $e');
    }

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Row(
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  fontFamily: "TiemposText",
                  color: Colors.black,
                ),
              ),
            ],
          ),
          content: Text(
            displayMessage,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              fontFamily: "TiemposText",
              color: Colors.grey[700],
              height: 1.4,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'OK',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  fontFamily: "TiemposText",
                  color: Colors.blue[600],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Shows the delete object confirmation dialog matching the design
  void _showDeleteObjectConfirmation(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            width: 400,
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with warning icon and close button
                Container(
                  padding: const EdgeInsets.only(bottom: 20),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Row(
                    children: [
                      // Warning icon
                      Container(
                        width: 24,
                        height: 24,
                        decoration: const BoxDecoration(
                          color: Color(0xFFFEF3C7), // Light yellow background
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.warning_amber_rounded,
                          color: Color(0xFFF59E0B), // Orange warning color
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Title
                      const Expanded(
                        child: Text(
                          'Attention',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      // Close button
                      IconButton(
                        onPressed: () => Navigator.of(dialogContext).pop(),
                        icon: const Icon(Icons.close),
                        iconSize: 20,
                        color: Colors.grey[600],
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // Main message
                const Text(
                  '"If you go back now, your document will not be saved. Do you want to continue editing and save your work instead?"',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 16),

                // Options text
                const Text(
                  'Options:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),

                // Option 1
                const Text(
                  'Continue Editing & Save',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),

                // Option 2
                const Text(
                  'Back Leave Without Saving',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 24),

                // Action buttons
                Container(
                  padding: const EdgeInsets.only(top: 20),
                  decoration: const BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Back button
                      TextButton(
                        onPressed: () {
                          Navigator.of(dialogContext)
                              .pop(); // Close the dialog first
                          // Navigate back without saving
                          Navigator.of(context).pop();
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                            side: BorderSide(color: Colors.grey[300]!),
                          ),
                        ),
                        child: const Text(
                          'Back',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.black87,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),

                      // Continue button
                      ElevatedButton(
                        onPressed: () {
                          Navigator.of(dialogContext).pop(); // Close the dialog
                          // Continue editing - stay on current screen
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              const Color(0xFF007AFF), // Blue color
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'Continue',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //backgroundColor: Color(0xfff6f6f6),
      body: Column(
        children: [
          // Top navigation bar
          _buildTopNavigation(context),
          // Main content area
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xffF7F9FB),
              ),
              child: messages.isEmpty
                  ? Center(
                      child: Container(
                        constraints: BoxConstraints(maxWidth: 1000),
                        padding: const EdgeInsets.symmetric(horizontal: 94),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                           
                              
                            // Close text aligned to the right
                            // const SizedBox(height: AppSpacing.sm),

                            // Three button containers
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    _buildButtonContainer('GO', () {
                                      Provider.of<WebHomeProvider>(context, listen: false)
                                          .currentScreenIndex = ScreenConstants.goComingSoonScreen;
                                    }),
                                    const SizedBox(width: 16),
                                    _buildButtonContainer('LO', () {
                                      // LO button action - you can add navigation here
                                      Logger.info('LO button pressed');
                                    }),
                                    const SizedBox(width: 16),
                                    _buildButtonContainer('Solution', () {
                                      // Solution button action - you can add navigation here
                                      Logger.info('Solution button pressed');
                                    }),
                                  ],
                                ),
                                 
                                  Align(
                              alignment: Alignment.centerRight,
                              child: InkWell(
                                onTap: () {
                                  // Direct navigation without callbacks
                                  try {
                                    Provider.of<WebHomeProvider>(context,
                                                listen: false)
                                            .currentScreenIndex =
                                        ScreenConstants
                                            .webCreationFlowMainScreen;
                                  } catch (e) {
                                    print('Navigation error: $e');
                                  }
                                },
                                child: Text(
                                  'Close',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: "TiemposText",
                                    color: Color(0xFF007AFF),
                                    decoration: TextDecoration.none,
                                  ),
                                ),
                              ),
                            ),

                           
                              
                              ],
                            ),

                            const SizedBox(height: AppSpacing.md),

                            // Chat field centered
                            ChatField(
                              isGeneralLoading: isLoading,
                              isFileLoading: false,
                              isSpeechLoading: false,
                              onSendMessage: _sendMessage,
                              onCancelRequest: _cancelRequest,
                              onFileSelected: _handleFileSelection,
                              onToggleRecording: _toggleRecording,
                              controller: chatController,
                              multimediaService: _multimediaService,
                              height: 180,
                            ),
                          ],
                        ),
                      ),
                    )
                  : Container(
                      padding: const EdgeInsets.symmetric(horizontal: 94),
                      child: Column(
                        children: [
                          // Messages list
                          Expanded(
                            child: ListView.builder(
                              controller: _chatScrollController,
                              padding: EdgeInsets.all(AppSpacing.sm),
                              itemCount: messages.length + (isLoading ? 1 : 0),
                              itemBuilder: (context, index) {
                                if (index == messages.length) {
                                  // Show loading indicator
                                  return Container(
                                    padding: EdgeInsets.symmetric(vertical: 20),
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                }

                                final message = messages[index];
                                return ChatMessageBubbleNew(
                                  message: message,
                                  index: index,
                                  isLastItem: index == messages.length - 1,
                                  multimediaService: _multimediaService,
                                  parentState: this,
                                  onNewLine: () {
                                    _scrollToBottom();
                                  },
                                  onComplete: () {
                                    if (mounted) _scrollToBottom();
                                  },
                                  suggestionsAsCheckbox: true,
                                );
                              },
                            ),
                          ),

                          // Close text aligned to the right above chat field
                          Align(
                            alignment: Alignment.centerRight,
                            child: InkWell(
                               onTap: () {
                                  // Direct navigation without callbacks
                                  try {
                                    Provider.of<WebHomeProvider>(context,
                                                listen: false)
                                            .currentScreenIndex =
                                        ScreenConstants
                                            .webCreationFlowMainScreen;
                                  } catch (e) {
                                    print('Navigation error: $e');
                                  }
                                },
                              child: Text(
                                'Close',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: "TiemposText",
                                  color: Color(0xFF007AFF),
                                  decoration: TextDecoration.none,
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 8),

                          // Chat field at the bottom
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: ChatField(
                              isGeneralLoading: isLoading,
                              isFileLoading: false,
                              isSpeechLoading: false,
                              onSendMessage: _sendMessage,
                              onCancelRequest: _cancelRequest,
                              onFileSelected: _handleFileSelection,
                              onToggleRecording: _toggleRecording,
                              controller: chatController,
                              multimediaService: _multimediaService,
                              height: 56,
                            ),
                          ),

                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonContainer(String label, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Color(0xffF7F9FB),
          border: Border.all(color: Colors.grey.shade600, width: 1),
          borderRadius: BorderRadius.circular(8),
          // boxShadow: [
          //   BoxShadow(
          //     color: Colors.black.withValues(alpha: 0.1),
          //     blurRadius: 4,
          //     offset: const Offset(0, 2),
          //   ),
          // ],
        ),
        child: Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Colors.black,
            fontFamily: "TiemposText",
          ),
        ),
      ),
    );
  }

  Widget _buildTopNavigation(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: const [
        Center(
          child: CustomTabHeader(),
        ),
      ],
    );
  }
}
