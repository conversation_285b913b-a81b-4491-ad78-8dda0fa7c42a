class Role {
  final String roleName;
  final RoleConfiguration roleConfiguration;
  final DepartmentConfiguration departmentConfiguration;
  final RoleInheritance roleInheritance;

  Role({
    required this.roleName,
    required this.roleConfiguration,
    required this.departmentConfiguration,
    required this.roleInheritance,
  });

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        roleName: json['roleName'],
        roleConfiguration: RoleConfiguration.fromJson(json['role_configuration']),
        departmentConfiguration: DepartmentConfiguration.fromJson(json['department_configuration']),
        roleInheritance: RoleInheritance.fromJson(json['role_inheritance']),
      );
}

class RoleConfiguration {
  final String roleName;
  final String description;
  final String reportsTo;
  final String organizationLevel;
  final String department;
  final String inherits;

  RoleConfiguration({
    required this.roleName,
    required this.description,
    required this.reportsTo,
    required this.organizationLevel,
    required this.department,
    required this.inherits,
  });

  factory RoleConfiguration.fromJson(Map<String, dynamic> json) => RoleConfiguration(
        roleName: json['roleName'],
        description: json['description'],
        reportsTo: json['reportsTo'],
        organizationLevel: json['organizationLevel'],
        department: json['department'],
        inherits: json['inherits'],
      );
}

class DepartmentConfiguration {
  final String departmentName;
  final String description;
  final String departmentHeadRole;
  final String? parentDepartment;

  DepartmentConfiguration({
    required this.departmentName,
    required this.description,
    required this.departmentHeadRole,
    this.parentDepartment,
  });

  factory DepartmentConfiguration.fromJson(Map<String, dynamic> json) =>
      DepartmentConfiguration(
        departmentName: json['departmentName'],
        description: json['description'],
        departmentHeadRole: json['departmentHeadRole'],
        parentDepartment: json['parentDepartment'],
      );
}

class RoleInheritance {
  final String parentRole;
  final String inheritsRole;

  RoleInheritance({
    required this.parentRole,
    required this.inheritsRole,
  });

  factory RoleInheritance.fromJson(Map<String, dynamic> json) => RoleInheritance(
        parentRole: json['parentRole'],
        inheritsRole: json['inheritsRole'],
      );
}
