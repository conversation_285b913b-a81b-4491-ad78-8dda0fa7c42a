/// Model for Solution creation and management
class SolutionCreationModel {
  final String? id;
  final String name;
  final String? displayName;
  final String? description;
  final String? businessPurpose;
  final String? businessDomain;
  final String? category;
  final String? colorTheme;
  final String? icon;
  final List<String>? tags;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  
  // Solution-specific properties
  final LODetails? loDetails;
  final InputsStack? inputsStack;
  final OutputStack? outputStack;
  final ValidationStack? validationStack;
  final UIStack? uiStack;
  final MappingStack? mappingStack;
  final NestedFunctionPathways? nestedFunctionPathways;
  final ExecutionPathway? executionPathway;

  SolutionCreationModel({
    this.id,
    required this.name,
    this.displayName,
    this.description,
    this.businessPurpose,
    this.businessDomain,
    this.category,
    this.colorTheme,
    this.icon,
    this.tags,
    this.createdAt,
    this.updatedAt,
    this.loDetails,
    this.inputsStack,
    this.outputStack,
    this.validationStack,
    this.uiStack,
    this.mappingStack,
    this.nestedFunctionPathways,
    this.executionPathway,
  });

  factory SolutionCreationModel.fromJson(Map<String, dynamic> json) {
    return SolutionCreationModel(
      id: json['id']?.toString(),
      name: json['name']?.toString() ?? '',
      displayName: json['displayName']?.toString(),
      description: json['description']?.toString(),
      businessPurpose: json['businessPurpose']?.toString(),
      businessDomain: json['businessDomain']?.toString(),
      category: json['category']?.toString(),
      colorTheme: json['colorTheme']?.toString(),
      icon: json['icon']?.toString(),
      tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      loDetails: json['loDetails'] != null ? LODetails.fromJson(json['loDetails']) : null,
      inputsStack: json['inputsStack'] != null ? InputsStack.fromJson(json['inputsStack']) : null,
      outputStack: json['outputStack'] != null ? OutputStack.fromJson(json['outputStack']) : null,
      validationStack: json['validationStack'] != null ? ValidationStack.fromJson(json['validationStack']) : null,
      uiStack: json['uiStack'] != null ? UIStack.fromJson(json['uiStack']) : null,
      mappingStack: json['mappingStack'] != null ? MappingStack.fromJson(json['mappingStack']) : null,
      nestedFunctionPathways: json['nestedFunctionPathways'] != null ? NestedFunctionPathways.fromJson(json['nestedFunctionPathways']) : null,
      executionPathway: json['executionPathway'] != null ? ExecutionPathway.fromJson(json['executionPathway']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'description': description,
      'businessPurpose': businessPurpose,
      'businessDomain': businessDomain,
      'category': category,
      'colorTheme': colorTheme,
      'icon': icon,
      'tags': tags,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'loDetails': loDetails?.toJson(),
      'inputsStack': inputsStack?.toJson(),
      'outputStack': outputStack?.toJson(),
      'validationStack': validationStack?.toJson(),
      'uiStack': uiStack?.toJson(),
      'mappingStack': mappingStack?.toJson(),
      'nestedFunctionPathways': nestedFunctionPathways?.toJson(),
      'executionPathway': executionPathway?.toJson(),
    };
  }

  SolutionCreationModel copyWith({
    String? id,
    String? name,
    String? displayName,
    String? description,
    String? businessPurpose,
    String? businessDomain,
    String? category,
    String? colorTheme,
    String? icon,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    LODetails? loDetails,
    InputsStack? inputsStack,
    OutputStack? outputStack,
    ValidationStack? validationStack,
    UIStack? uiStack,
    MappingStack? mappingStack,
    NestedFunctionPathways? nestedFunctionPathways,
    ExecutionPathway? executionPathway,
  }) {
    return SolutionCreationModel(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      description: description ?? this.description,
      businessPurpose: businessPurpose ?? this.businessPurpose,
      businessDomain: businessDomain ?? this.businessDomain,
      category: category ?? this.category,
      colorTheme: colorTheme ?? this.colorTheme,
      icon: icon ?? this.icon,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      loDetails: loDetails ?? this.loDetails,
      inputsStack: inputsStack ?? this.inputsStack,
      outputStack: outputStack ?? this.outputStack,
      validationStack: validationStack ?? this.validationStack,
      uiStack: uiStack ?? this.uiStack,
      mappingStack: mappingStack ?? this.mappingStack,
      nestedFunctionPathways: nestedFunctionPathways ?? this.nestedFunctionPathways,
      executionPathway: executionPathway ?? this.executionPathway,
    );
  }
}

/// LO Details model
class LODetails {
  final String? status;
  final int? count;
  final LeaveApplicationObjectConfiguration? leaveApplicationObjectConfiguration;

  LODetails({
    this.status,
    this.count,
    this.leaveApplicationObjectConfiguration,
  });

  factory LODetails.fromJson(Map<String, dynamic> json) {
    return LODetails(
      status: json['status']?.toString(),
      count: json['count'] as int?,
      leaveApplicationObjectConfiguration: json['leaveApplicationObjectConfiguration'] != null 
        ? LeaveApplicationObjectConfiguration.fromJson(json['leaveApplicationObjectConfiguration']) 
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'count': count,
      'leaveApplicationObjectConfiguration': leaveApplicationObjectConfiguration?.toJson(),
    };
  }
}

/// Leave Application Object Configuration model
class LeaveApplicationObjectConfiguration {
  final String? name;
  final String? displayName;
  final String? workflowOrigin;
  final String? pathwayType;
  final String? functionType;
  final String? agentType;
  final String? uiType;
  final String? roleName;

  LeaveApplicationObjectConfiguration({
    this.name,
    this.displayName,
    this.workflowOrigin,
    this.pathwayType,
    this.functionType,
    this.agentType,
    this.uiType,
    this.roleName,
  });

  factory LeaveApplicationObjectConfiguration.fromJson(Map<String, dynamic> json) {
    return LeaveApplicationObjectConfiguration(
      name: json['name']?.toString(),
      displayName: json['displayName']?.toString(),
      workflowOrigin: json['workflowOrigin']?.toString(),
      pathwayType: json['pathwayType']?.toString(),
      functionType: json['functionType']?.toString(),
      agentType: json['agentType']?.toString(),
      uiType: json['uiType']?.toString(),
      roleName: json['roleName']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'displayName': displayName,
      'workflowOrigin': workflowOrigin,
      'pathwayType': pathwayType,
      'functionType': functionType,
      'agentType': agentType,
      'uiType': uiType,
      'roleName': roleName,
    };
  }
}

/// Inputs Stack model
class InputsStack {
  final String? status;
  final int? attributesCount;

  InputsStack({
    this.status,
    this.attributesCount,
  });

  factory InputsStack.fromJson(Map<String, dynamic> json) {
    return InputsStack(
      status: json['status']?.toString(),
      attributesCount: json['attributesCount'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'attributesCount': attributesCount,
    };
  }
}

/// Output Stack model
class OutputStack {
  final String? status;
  final int? configuredCount;
  final List<OutputStackRow>? rows;

  OutputStack({
    this.status,
    this.configuredCount,
    this.rows,
  });

  factory OutputStack.fromJson(Map<String, dynamic> json) {
    return OutputStack(
      status: json['status']?.toString(),
      configuredCount: json['configuredCount'] as int?,
      rows: json['rows'] != null
        ? (json['rows'] as List).map((row) => OutputStackRow.fromJson(row)).toList()
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'configuredCount': configuredCount,
      'rows': rows?.map((row) => row.toJson()).toList(),
    };
  }
}

/// Output Stack Row model
class OutputStackRow {
  final String entity;
  final String attribute;
  final bool required;

  OutputStackRow({
    required this.entity,
    required this.attribute,
    required this.required,
  });

  factory OutputStackRow.fromJson(Map<String, dynamic> json) {
    return OutputStackRow(
      entity: json['entity']?.toString() ?? '',
      attribute: json['attribute']?.toString() ?? '',
      required: json['required'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entity': entity,
      'attribute': attribute,
      'required': required,
    };
  }
}

/// Validation Stack model
class ValidationStack {
  final String? status;
  final int? rulesCount;
  final List<ValidationStackRow>? rows;

  ValidationStack({
    this.status,
    this.rulesCount,
    this.rows,
  });

  factory ValidationStack.fromJson(Map<String, dynamic> json) {
    return ValidationStack(
      status: json['status']?.toString(),
      rulesCount: json['rulesCount'] as int?,
      rows: json['rows'] != null
        ? (json['rows'] as List).map((row) => ValidationStackRow.fromJson(row)).toList()
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'rulesCount': rulesCount,
      'rows': rows?.map((row) => row.toJson()).toList(),
    };
  }
}

/// Validation Stack Row model
class ValidationStackRow {
  final String attribute;
  final String validationFunction;
  final String successCondition;
  final String failureCondition;
  final String successMessage;
  final String failureMessage;

  ValidationStackRow({
    required this.attribute,
    required this.validationFunction,
    required this.successCondition,
    required this.failureCondition,
    required this.successMessage,
    required this.failureMessage,
  });

  factory ValidationStackRow.fromJson(Map<String, dynamic> json) {
    return ValidationStackRow(
      attribute: json['attribute']?.toString() ?? '',
      validationFunction: json['validationFunction']?.toString() ?? '',
      successCondition: json['successCondition']?.toString() ?? '',
      failureCondition: json['failureCondition']?.toString() ?? '',
      successMessage: json['successMessage']?.toString() ?? '',
      failureMessage: json['failureMessage']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'attribute': attribute,
      'validationFunction': validationFunction,
      'successCondition': successCondition,
      'failureCondition': failureCondition,
      'successMessage': successMessage,
      'failureMessage': failureMessage,
    };
  }
}

/// UI Stack model
class UIStack {
  final String? status;
  final int? configuredCount;
  final List<UIStackRow>? rows;

  UIStack({
    this.status,
    this.configuredCount,
    this.rows,
  });

  factory UIStack.fromJson(Map<String, dynamic> json) {
    return UIStack(
      status: json['status']?.toString(),
      configuredCount: json['configuredCount'] as int?,
      rows: json['rows'] != null
        ? (json['rows'] as List).map((row) => UIStackRow.fromJson(row)).toList()
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'configuredCount': configuredCount,
      'rows': rows?.map((row) => row.toJson()).toList(),
    };
  }
}

/// UI Stack Row model
class UIStackRow {
  final String entityName;
  final String displayName;
  final String widgetType;
  final String selectionType;
  final String editingRights;
  final int pagination;
  final String search;
  final String filters;
  final String sort;
  final List<Map<String, dynamic>> dataFiltering;
  final List<Map<String, dynamic>> dataSorting;
  final List<Map<String, dynamic>> conditionerColors;

  UIStackRow({
    required this.entityName,
    required this.displayName,
    required this.widgetType,
    required this.selectionType,
    required this.editingRights,
    required this.pagination,
    required this.search,
    required this.filters,
    required this.sort,
    required this.dataFiltering,
    required this.dataSorting,
    required this.conditionerColors,
  });

  factory UIStackRow.fromJson(Map<String, dynamic> json) {
    return UIStackRow(
      entityName: json['entityName']?.toString() ?? '',
      displayName: json['displayName']?.toString() ?? '',
      widgetType: json['widgetType']?.toString() ?? '',
      selectionType: json['selectionType']?.toString() ?? '',
      editingRights: json['editingRights']?.toString() ?? '',
      pagination: json['pagination'] as int? ?? 0,
      search: json['search']?.toString() ?? '',
      filters: json['filters']?.toString() ?? '',
      sort: json['sort']?.toString() ?? '',
      dataFiltering: json['dataFiltering'] != null
        ? List<Map<String, dynamic>>.from(json['dataFiltering'])
        : [],
      dataSorting: json['dataSorting'] != null
        ? List<Map<String, dynamic>>.from(json['dataSorting'])
        : [],
      conditionerColors: json['conditionerColors'] != null
        ? List<Map<String, dynamic>>.from(json['conditionerColors'])
        : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entityName': entityName,
      'displayName': displayName,
      'widgetType': widgetType,
      'selectionType': selectionType,
      'editingRights': editingRights,
      'pagination': pagination,
      'search': search,
      'filters': filters,
      'sort': sort,
      'dataFiltering': dataFiltering,
      'dataSorting': dataSorting,
      'conditionerColors': conditionerColors,
    };
  }
}

/// Mapping Stack model
class MappingStack {
  final String? status;
  final int? mappingCount;
  final List<MappingStackRow>? rows;

  MappingStack({
    this.status,
    this.mappingCount,
    this.rows,
  });

  factory MappingStack.fromJson(Map<String, dynamic> json) {
    return MappingStack(
      status: json['status']?.toString(),
      mappingCount: json['mappingCount'] as int?,
      rows: json['rows'] != null
        ? (json['rows'] as List).map((row) => MappingStackRow.fromJson(row)).toList()
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'mappingCount': mappingCount,
      'rows': rows?.map((row) => row.toJson()).toList(),
    };
  }
}

/// Mapping Stack Row model
class MappingStackRow {
  final String sourceGOName;
  final String sourceLOName;
  final String sourceType;
  final String sourceEntity;
  final String sourceAttribute;
  final String targetGOName;
  final String targetLOName;
  final String targetType;
  final String targetEntity;
  final String targetAttribute;

  MappingStackRow({
    required this.sourceGOName,
    required this.sourceLOName,
    required this.sourceType,
    required this.sourceEntity,
    required this.sourceAttribute,
    required this.targetGOName,
    required this.targetLOName,
    required this.targetType,
    required this.targetEntity,
    required this.targetAttribute,
  });

  factory MappingStackRow.fromJson(Map<String, dynamic> json) {
    return MappingStackRow(
      sourceGOName: json['sourceGOName']?.toString() ?? '',
      sourceLOName: json['sourceLOName']?.toString() ?? '',
      sourceType: json['sourceType']?.toString() ?? '',
      sourceEntity: json['sourceEntity']?.toString() ?? '',
      sourceAttribute: json['sourceAttribute']?.toString() ?? '',
      targetGOName: json['targetGOName']?.toString() ?? '',
      targetLOName: json['targetLOName']?.toString() ?? '',
      targetType: json['targetType']?.toString() ?? '',
      targetEntity: json['targetEntity']?.toString() ?? '',
      targetAttribute: json['targetAttribute']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sourceGOName': sourceGOName,
      'sourceLOName': sourceLOName,
      'sourceType': sourceType,
      'sourceEntity': sourceEntity,
      'sourceAttribute': sourceAttribute,
      'targetGOName': targetGOName,
      'targetLOName': targetLOName,
      'targetType': targetType,
      'targetEntity': targetEntity,
      'targetAttribute': targetAttribute,
    };
  }
}

/// Nested Function Pathways model
class NestedFunctionPathways {
  final String? status;
  final int? pathwaysCount;
  final List<NestedFunctionPathwaysRow>? rows;

  NestedFunctionPathways({
    this.status,
    this.pathwaysCount,
    this.rows,
  });

  factory NestedFunctionPathways.fromJson(Map<String, dynamic> json) {
    return NestedFunctionPathways(
      status: json['status']?.toString(),
      pathwaysCount: json['pathwaysCount'] as int?,
      rows: json['rows'] != null
        ? (json['rows'] as List).map((row) => NestedFunctionPathwaysRow.fromJson(row)).toList()
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'pathwaysCount': pathwaysCount,
      'rows': rows?.map((row) => row.toJson()).toList(),
    };
  }
}

/// Nested Function Pathways Row model
class NestedFunctionPathwaysRow {
  final String functionId;
  final String functionName;
  final String functionType;
  final String description;

  NestedFunctionPathwaysRow({
    required this.functionId,
    required this.functionName,
    required this.functionType,
    required this.description,
  });

  factory NestedFunctionPathwaysRow.fromJson(Map<String, dynamic> json) {
    return NestedFunctionPathwaysRow(
      functionId: json['functionId']?.toString() ?? '',
      functionName: json['functionName']?.toString() ?? '',
      functionType: json['functionType']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'functionId': functionId,
      'functionName': functionName,
      'functionType': functionType,
      'description': description,
    };
  }
}

/// Execution Pathway model
class ExecutionPathway {
  final String? status;
  final int? routesCount;
  final List<ExecutionPathwayRow>? rows;

  ExecutionPathway({
    this.status,
    this.routesCount,
    this.rows,
  });

  factory ExecutionPathway.fromJson(Map<String, dynamic> json) {
    return ExecutionPathway(
      status: json['status']?.toString(),
      routesCount: json['routesCount'] as int?,
      rows: json['rows'] != null
        ? (json['rows'] as List).map((row) => ExecutionPathwayRow.fromJson(row)).toList()
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'routesCount': routesCount,
      'rows': rows?.map((row) => row.toJson()).toList(),
    };
  }
}

/// Execution Pathway Row model
class ExecutionPathwayRow {
  final String condition;
  final String functionName;
  final String description;

  ExecutionPathwayRow({
    required this.condition,
    required this.functionName,
    required this.description,
  });

  factory ExecutionPathwayRow.fromJson(Map<String, dynamic> json) {
    return ExecutionPathwayRow(
      condition: json['condition']?.toString() ?? '',
      functionName: json['functionName']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'condition': condition,
      'functionName': functionName,
      'description': description,
    };
  }
}
