import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/theme/spacing.dart';

import '../../../../../models/customer_model.dart';

/// Reusable Form Table Widget with Synchronized Scrolling
/// Supports both business rules data and generic form data structures
class FormTableWidget extends StatefulWidget {
  // Generic data support
  final List<Map<String, dynamic>>? genericData;
  final List<String>? headers;
  final List<double>? columnWidths;
  final String? title;

  // Business rules specific support (backward compatibility)
  final List<BusinessRule1>? businessRulesData;
  final Function(String, BusinessRule1)? onUpdateRule;

  // Table configuration
  final bool showAddButton;
  final String addButtonText;
  final VoidCallback? onAddPressed;
  final bool enableActions;
  final Function(int, Map<String, dynamic>)? onEditRow;
  final Function(int)? onDeleteRow;

  // Styling configuration
  final double? maxHeight;
  final bool showScrollbar;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const FormTableWidget({
    super.key,
    // Generic parameters
    this.genericData,
    this.headers,
    this.columnWidths,
    this.title,
    // Business rules parameters (backward compatibility)
    this.businessRulesData,
    this.onUpdateRule,
    // Configuration parameters
    this.showAddButton = true,
    this.addButtonText = 'Add Field',
    this.onAddPressed,
    this.enableActions = true,
    this.onEditRow,
    this.onDeleteRow,
    // Styling parameters
    this.maxHeight = 200,
    this.showScrollbar = true,
    this.padding,
    this.margin,
  });

  @override
  State<FormTableWidget> createState() => _FormTableWidgetState();
}

class _FormTableWidgetState extends State<FormTableWidget> {
  // Synchronized scroll controllers for Excel-like frozen pane behavior
  final ScrollController _verticalController = ScrollController();
  final ScrollController _actionsController = ScrollController();

  late List<Map<String, dynamic>> _tableData;
  late List<String> _tableHeaders;
  late List<double> _columnWidths;

  @override
  void initState() {
    super.initState();
    _initializeTableData();
    _setupScrollSynchronization();
  }

  void _initializeTableData() {
    if (widget.businessRulesData != null) {
      // Backward compatibility: Convert business rules to generic format
      _tableData = widget.businessRulesData!
          .map((rule) => {
                'field': rule.field,
                'value': rule.value,
                'source': rule.source,
                'sourceColor': rule.sourceColor, // Keep for badge coloring
              })
          .toList();
      _tableHeaders = [
        'Field',
        'Value',
        'Source'
      ]; // Removed Source Color column
      _columnWidths = [150, 200, 120]; // Adjusted for 3 columns
    } else if (widget.genericData != null) {
      // Use provided generic data
      _tableData = List.from(widget.genericData!);
      _tableHeaders = widget.headers ?? [];
      _columnWidths = widget.columnWidths ?? [];
    } else {
      // Empty state
      _tableData = [];
      _tableHeaders = [];
      _columnWidths = [];
    }
  }

  void _setupScrollSynchronization() {
    // Set up bidirectional scroll synchronization for Excel-like frozen pane behavior
    _verticalController.addListener(_syncScrollControllers);
    _actionsController.addListener(_syncFromActionsController);
  }

  /// Synchronizes actions controller to match vertical controller position
  void _syncScrollControllers() {
    if (_verticalController.hasClients && _actionsController.hasClients) {
      if (_actionsController.offset != _verticalController.offset) {
        _actionsController.jumpTo(_verticalController.offset);
      }
    }
  }

  /// Synchronizes vertical controller to match actions controller position
  void _syncFromActionsController() {
    if (_verticalController.hasClients && _actionsController.hasClients) {
      if (_verticalController.offset != _actionsController.offset) {
        _verticalController.jumpTo(_actionsController.offset);
      }
    }
  }

  @override
  void dispose() {
    _verticalController.removeListener(_syncScrollControllers);
    _actionsController.removeListener(_syncFromActionsController);
    _verticalController.dispose();
    _actionsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding ?? EdgeInsets.all(AppSpacing.xs),
      margin: widget.margin ?? const EdgeInsets.symmetric(horizontal: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: AppSpacing.xs),
          if (widget.showAddButton) _buildHeader(context),
          SizedBox(height: AppSpacing.sm),
          _buildScrollableFormTable(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.title ?? 'Table Configuration',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelSmall(context),
            fontWeight: FontWeight.w600,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        ElevatedButton.icon(
          onPressed: widget.onAddPressed ?? _handleAddRule,
          icon: const Icon(Icons.add, size: 12),
          label: Text(
            widget.addButtonText,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelSmall(context),
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF0058FF),
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(
              horizontal: AppSpacing.sm,
              vertical: AppSpacing.xs,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSpacing.xs),
            ),
          ),
        ),
      ],
    );
  }

  void _handleAddRule() {
    if (widget.businessRulesData != null) {
      // Business rules specific logic - use enhanced dialog
      _showAddBusinessRuleDialog(context);
    } else {
      // Generic add logic - show enhanced dialog
      _showEnhancedDialog(
        context: context,
        title: 'Add Field',
        isEditMode: false,
        sectionTitle: widget.title ?? 'Object Details',
        editIndex: null,
        editData: null, // No pre-populated data for add mode
      );
    }
  }

  void _showAddBusinessRuleDialog(BuildContext context) {
    // Use enhanced dialog for adding business rules
    _showEnhancedDialog(
      context: context,
      title: 'Add Field',
      isEditMode: false,
      sectionTitle: 'Object Details', // Use Object Details for business rules
      editIndex: null,
      editData: null, // No pre-populated data for add mode
    );
  }

  /// Build the main scrollable form table with synchronized scrolling
  Widget _buildScrollableFormTable(BuildContext context) {
    if (_tableData.isEmpty) {
      return _buildEmptyState(context);
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
        borderRadius: BorderRadius.circular(6),
        color: Colors.white,
      ),
      child: Row(
        children: [
          // Scrollable data columns section (header + body combined)
          Expanded(
            child: _tableHeaders.asMap().entries.length <= 4
                ? _buildValuesTable(context, true)
                : SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: _buildValuesTable(context, false),
                  ),
          ),
          // Fixed Actions column
          if (widget.enableActions)
            Container(
              width: 100,
              decoration: const BoxDecoration(
                border: Border(
                  left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Column(
                children: [
                  // Actions header
                  Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: AppSpacing.xs, vertical: AppSpacing.xs),
                    decoration: const BoxDecoration(
                      color: Color(0xFFF9FAFB),
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(6),
                      ),
                      border: Border(
                        bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                      ),
                    ),
                    child: Center(
                      child: Text(
                        'ACTIONS',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.labelMedium(context),
                          fontWeight: FontWeight.w600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ),
                  // Actions body with synchronized scrolling
                  Container(
                    constraints:
                        BoxConstraints(maxHeight: widget.maxHeight ?? 200),
                    child: widget.showScrollbar
                        ? Scrollbar(
                            controller: _actionsController,
                            thumbVisibility: true,
                            child: SingleChildScrollView(
                              controller: _actionsController,
                              child: Column(
                                children: _buildFixedActionsColumn(context),
                              ),
                            ),
                          )
                        : SingleChildScrollView(
                            controller: _actionsController,
                            child: Column(
                              children: _buildFixedActionsColumn(context),
                            ),
                          ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildValuesTable(context, isExpanded) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header row
        Container(
          decoration: const BoxDecoration(
            color: Color(0xFFF9FAFB),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(6),
            ),
            border: Border(
              bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: _buildScrollableFormTableHeader(context, isExpanded),
        ),
        // Data rows with vertical scrolling
        Container(
          constraints: BoxConstraints(maxHeight: widget.maxHeight ?? 200),
          child: ScrollConfiguration(
            behavior: ScrollConfiguration.of(context).copyWith(
              scrollbars: false, // Disable scrollbar painting
            ),
            child: SingleChildScrollView(
              controller: _verticalController,
              child: Column(
                children: _buildScrollableFormTableRows(context, isExpanded),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
        borderRadius: BorderRadius.circular(6),
        color: Colors.grey[50],
      ),
      child: Column(
        children: [
          Icon(
            Icons.table_chart_outlined,
            size: 48,
            color: Colors.grey[400],
          ),
          SizedBox(height: AppSpacing.sm),
          Text(
            'No data available',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: AppSpacing.xs),
          Text(
            'Click "${widget.addButtonText}" to add your first entry',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// Build the scrollable form table header (without actions column)
  Widget _buildScrollableFormTableHeader(
      BuildContext context, bool isExpanded) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: _tableHeaders.asMap().entries.map((entry) {
            int index = entry.key;
            String header = entry.value;
            double width = isExpanded
                ? constraints.maxWidth / _tableHeaders.asMap().entries.length
                : index < _columnWidths.length
                    ? _columnWidths[index]
                    : 120;
            return SizedBox(
              width: width,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Tooltip(
                  message: header, // full text displayed on hover
                  child: Text(
                    header,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelMedium(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.grey[700],
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ),
            );
          }).toList(),
        );
      },
    );
  }

  /// Build the scrollable form table rows (without actions column)
  List<Widget> _buildScrollableFormTableRows(
      BuildContext context, bool isExpanded) {
    return _tableData.asMap().entries.map((entry) {
      int rowIndex = entry.key;
      Map<String, dynamic> rowData = entry.value;

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: const BoxDecoration(
          border: Border(
            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
          ),
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: _tableHeaders.asMap().entries.map((headerEntry) {
                int colIndex = headerEntry.key;
                String header = headerEntry.value;
                double width = isExpanded
                    ? constraints.maxWidth /
                        _tableHeaders.asMap().entries.length
                    : colIndex < _columnWidths.length
                        ? _columnWidths[colIndex]
                        : 120;

                // Get the data value for this column
                String dataValue = _getDataValue(rowData, header, colIndex);

                return SizedBox(
                  width: width,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: _buildTableCellContent(
                        context, dataValue, colIndex, rowIndex),
                  ),
                );
              }).toList(),
            );
          },
        ),
      );
    }).toList();
  }

  /// Build the fixed actions column with synchronized scrolling
  List<Widget> _buildFixedActionsColumn(BuildContext context) {
    return _tableData.asMap().entries.map((entry) {
      int index = entry.key;
      Map<String, dynamic> rowData = entry.value;

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 5),
        decoration: const BoxDecoration(
          border: Border(
            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              onPressed: () => _handleEditRow(context, index, rowData),
              icon: const Icon(Icons.edit_outlined),
              color: Colors.blue[600],
              iconSize: 18,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
              tooltip: 'Edit',
            ),
            SizedBox(width: AppSpacing.xxs),
            IconButton(
              onPressed: () => _handleDeleteRow(context, index),
              icon: const Icon(Icons.delete_outline),
              color: Colors.red[600],
              iconSize: 18,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
              tooltip: 'Delete',
            ),
          ],
        ),
      );
    }).toList();
  }

  /// Get data value for a specific column
  String _getDataValue(
      Map<String, dynamic> rowData, String header, int colIndex) {
    // Try to get value by header name first
    String key = header.toLowerCase().replaceAll(' ', '');
    if (rowData.containsKey(key)) {
      return rowData[key]?.toString() ?? '';
    }

    // Try original header name
    if (rowData.containsKey(header)) {
      return rowData[header]?.toString() ?? '';
    }

    // For business rules, map headers to correct keys
    if (widget.businessRulesData != null) {
      switch (header.toLowerCase()) {
        case 'field':
          return rowData['field']?.toString() ?? '';
        case 'value':
          return rowData['value']?.toString() ?? '';
        case 'source':
          return rowData['source']?.toString() ?? '';
      }
    }

    // Try by index if available (excluding sourceColor from display)
    List<String> displayValues = [];
    if (widget.businessRulesData != null) {
      // Only include field, value, source for display
      displayValues = [
        rowData['field']?.toString() ?? '',
        rowData['value']?.toString() ?? '',
        rowData['source']?.toString() ?? '',
      ];
    } else {
      displayValues = rowData.values.map((v) => v?.toString() ?? '').toList();
    }

    if (colIndex < displayValues.length) {
      return displayValues[colIndex];
    }

    return '';
  }

  /// Build table cell content with appropriate styling
  Widget _buildTableCellContent(
      BuildContext context, String data, int colIndex, int rowIndex) {
    // For business rules data or Role Assignment data, show colored badge for Source column (index 2)
    if (((widget.businessRulesData != null) ||
            (widget.title == 'Process Ownership' ||
                widget.title == 'Role Assignment')) &&
        colIndex == 2 &&
        _tableHeaders[colIndex] == 'Source') {
      return _buildSourceBadge(data, rowIndex);
    }

    return Tooltip(
      message: data,
      child: Text(
        data,
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodySmall(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black87,
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  /// Build colored badge for source information (matching extract_details_middle_static.dart)
  Widget _buildSourceBadge(String source, int rowIndex) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      decoration: BoxDecoration(
        color: _getSourceBackgroundColor(source),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        source,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: _getSourceTextColor(source),
          fontFamily: FontManager.fontFamilyTiemposText,
        ),
      ),
    );
  }

  /// Get background color for source type (matching extract_details_middle_static.dart)
  Color _getSourceBackgroundColor(String source) {
    switch (source.toLowerCase()) {
      case 'extracted':
        return const Color(0xFFD1FAE5); // Green background
      case 'auto-generated':
        return const Color(0xFFDBEAFE); // Blue background
      case 'inferred':
        return const Color(0xFFFEF3C7); // Orange/Yellow background
      case 'generated':
        return const Color(0xFFE9D5FF); // Purple background
      default:
        return const Color(0xFFF3F4F6); // Gray background
    }
  }

  /// Get text color for source type (matching extract_details_middle_static.dart)
  Color _getSourceTextColor(String source) {
    switch (source.toLowerCase()) {
      case 'extracted':
        return const Color(0xFF065F46); // Green text
      case 'auto-generated':
        return const Color(0xFF1E40AF); // Blue text
      case 'inferred':
        return const Color(0xFF92400E); // Orange text
      case 'generated':
        return const Color(0xFF7C3AED); // Purple text
      default:
        return const Color(0xFF374151); // Gray text
    }
  }

  /// Handle edit row action
  void _handleEditRow(
      BuildContext context, int index, Map<String, dynamic> rowData) {
    if (widget.onEditRow != null) {
      widget.onEditRow!(index, rowData);
    } else if (widget.businessRulesData != null &&
        widget.onUpdateRule != null) {
      // Backward compatibility for business rules
      _showEditBusinessRuleDialog(context, index, rowData);
    } else {
      // Show enhanced dialog for generic data
      _showEnhancedDialog(
        context: context,
        title: 'Edit Field',
        isEditMode: true,
        sectionTitle: widget.title ?? 'Object Details',
        editIndex: index,
        editData: _convertRowDataToList(rowData),
      );
    }
  }

  /// Handle delete row action
  void _handleDeleteRow(BuildContext context, int index) {
    if (widget.onDeleteRow != null) {
      widget.onDeleteRow!(index);
    } else {
      _showDeleteConfirmationDialog(context, index);
    }
  }

  void _showEditBusinessRuleDialog(
      BuildContext context, int index, Map<String, dynamic> rowData) {
    // Use enhanced dialog for business rules too
    _showEnhancedDialog(
      context: context,
      title: 'Edit Business Rule',
      isEditMode: true,
      sectionTitle: 'Object Details', // Use Object Details for business rules
      editIndex: index,
      editData: _convertRowDataToList(rowData),
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Entry'),
        content: Text('Are you sure you want to delete this entry?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _tableData.removeAt(index);
              });
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Convert row data map to list format for dialog initialization
  List<String> _convertRowDataToList(Map<String, dynamic> rowData) {
    final List<String> result = [];

    for (String header in _tableHeaders) {
      String value = '';

      // For business rules data, the keys are lowercase
      final lowercaseKey = header.toLowerCase();

      // Try multiple key formats to match the data
      if (rowData.containsKey(lowercaseKey)) {
        value = rowData[lowercaseKey]?.toString() ?? '';
      }
      // Try exact header match
      else if (rowData.containsKey(header)) {
        value = rowData[header]?.toString() ?? '';
      }
      // Try header without spaces and lowercase
      else {
        final key = header.toLowerCase().replaceAll(' ', '');
        value = rowData[key]?.toString() ?? '';
      }

      result.add(value);
    }

    return result;
  }

  /// Enhanced dialog with scrolling support and comprehensive field mapping
  /// Replicates the exact design from extract_details_middle_static.dart
  void _showEnhancedDialog({
    required BuildContext context,
    required String title,
    required bool isEditMode,
    required String sectionTitle,
    int? editIndex,
    List<String>? editData,
  }) {
    // Create controllers for all possible fields
    final Map<String, TextEditingController> controllers = {};
    final Map<String, String> dropdownValues = {};

    // Initialize controllers and values based on section
    _initializeDialogFields(
        sectionTitle, controllers, dropdownValues, editData);

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Container(
                width: math.min(MediaQuery.of(context).size.width * 0.8, 534),
                height: math.min(MediaQuery.of(context).size.height * 0.8, 600),
                child: Column(
                  children: [
                    // Header with bottom border
                    Container(
                      padding: const EdgeInsets.only(
                          bottom: 14, top: 14, left: 24, right: 24),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom:
                              BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyLarge(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Colors.black,
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(Icons.close),
                            iconSize: 24,
                            color: Colors.grey[600],
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),
                    ),

                    // Scrollable content area
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(24),
                        child: _buildEnhancedFormFields(context, sectionTitle,
                            controllers, dropdownValues, setState),
                      ),
                    ),

                    // Footer with top border
                    Container(
                      padding: const EdgeInsets.only(
                          bottom: 14, top: 14, left: 24, right: 24),
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: BorderSide(color: Colors.grey[300]!),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: () {
                              _handleDialogSubmit(
                                context,
                                sectionTitle,
                                controllers,
                                dropdownValues,
                                isEditMode,
                                editIndex,
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0058FF),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              isEditMode
                                  ? 'Update'
                                  : _getButtonText(sectionTitle),
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Initialize dialog fields based on section type
  /// Replicates the logic from extract_details_middle_static.dart
  void _initializeDialogFields(
    String sectionTitle,
    Map<String, TextEditingController> controllers,
    Map<String, String> dropdownValues,
    List<String>? editData,
  ) {
    final headers = _getDialogHeaders(sectionTitle);
    final headerToKeyMap = _getHeaderToKeyMapping();

    for (int i = 0; i < headers.length; i++) {
      final header = headers[i];
      final key = headerToKeyMap[header] ?? header.toLowerCase();
      final value = editData != null && i < editData.length ? editData[i] : '';

      // Determine if this field should be a dropdown
      if (_isDropdownField(sectionTitle, header)) {
        // Get available options for this dropdown
        final options = _getDropdownOptionsForField(sectionTitle, header);

        // Validate that the current value exists in options, otherwise use default
        String validValue;
        if (value.isNotEmpty && options.contains(value)) {
          validValue = value;
        } else {
          validValue = _getDefaultDropdownValue(sectionTitle, header);
          // Ensure default value exists in options
          if (!options.contains(validValue)) {
            validValue = options.isNotEmpty ? options.first : '';
          }
        }

        dropdownValues[key] = validValue;
      } else {
        controllers[key] = TextEditingController(text: value);
      }
    }
  }

  /// Get dialog headers for different sections (excludes read-only fields)
  List<String> _getDialogHeaders(String title) {
    switch (title) {
      case 'Object Details':
        return ['FIELD', 'VALUE', 'SOURCE'];
      case 'Entity Relationships':
        return [
          'RELATEDENTITY',
          'RELATIONSHIPTYPE',
          'FOREIGNKEY',
          'DESCRIPTION',
          'STATUS'
        ];
      case 'Attribute Business Rules':
        return ['ATTRIBUTENAME', 'OPERATOR', 'VALUE', 'ERRORMESSAGE', 'STATUS'];
      case 'Enumerated Values':
        return [
          'ENTITYATTRIBUTE',
          'ENUMNAME',
          'VALUE',
          'DISPLAY',
          'DESCRIPTION',
          'SORTORDER',
          'ACTIVE'
        ];
      case 'System Permissions':
        return [
          'PERMISSIONID',
          'PERMISSIONNAME',
          'PERMISSIONTYPE',
          'RESOURCEIDENTIFIER',
          'ACTIONS',
          'DESCRIPTION',
          'SCOPE',
          'NATURALLANGUAGE',
          'VERSION',
          'STATUS'
        ];
      case 'Security Classification':
        return [
          'ENTITYATTRIBUTE',
          'CLASSIFICATION',
          'PIITYPE',
          'ENCRYPTIONREQUIRED',
          'ENCRYPTIONTYPE',
          'MASKINGREQUIRED',
          'MASKINGPATTERN',
          'ACCESSLEVEL',
          'AUDITTRAIL'
        ];
      case 'Process Ownership':
        return ['ROLETYPE', 'ASSIGNEDROLE', 'SOURCE'];
      case 'Role Assignment':
        return ['ROLETYPE', 'ASSIGNEDROLE']; // ✅ Only 2 fields for dialog
      case 'Trigger Definition':
      case 'Trigger Configuration':
        return [
          'TRIGGERTYPE',
          'TRIGGERATTRIBUTES',
          'TRIGGERCONDITION',
          'TRIGGERSCHEDULE'
        ];
      case 'Objective Management':
        return ['LOID', 'OBJECTIVENAME', 'AGENTTYPE'];
      case 'Pathway Definitions Configuration':
        return ['PATHWAYID', 'PATHWAYNAME', 'DESCRIPTION', 'STATUS'];
      case 'Pathway Routing Configuration':
        return ['OBJECTIVE', 'AGENTTYPE', 'ROUTETYPE', 'ROUTINGCONDITIONS'];
      case 'Business Rules Configuration':
        return [
          'ROLETYPE',
          'ENTITY',
          'DESCRIPTION',
          'INPUTS',
          'OPERATION',
          'OUTPUT',
          'VALIDATION'
        ];
      case 'Validation Rules Configuration':
        return [
          'RULENAME',
          'DESCRIPTION',
          'INPUTS',
          'OPERATION',
          'OUTPUT',
          'VALIDATIONTYPE',
          'ERRORMESSAGE'
        ];
      default:
        return ['FIELD', 'VALUE', 'SOURCE'];
    }
  }

  /// Get context-aware button text for different sections
  String _getButtonText(String sectionTitle) {
    switch (sectionTitle) {
      case 'Role Assignment':
        return 'Add Role';
      case 'Trigger Configuration':
      case 'Trigger Definition':
        return 'Add Trigger Definition';
      case 'Objective Management':
        return 'Add Objective';
      case 'Pathway Definitions Configuration':
        return 'Add Pathway Definition';
      case 'Pathway Routing Configuration':
        return 'Add Pathway';
      case 'Business Rules Configuration':
        return 'Add Rule';
      case 'Validation Rules Configuration':
        return 'Add Validation';
      default:
        return 'Add Field';
    }
  }

  /// Get comprehensive header to key mapping
  Map<String, String> _getHeaderToKeyMapping() {
    return {
      // Object Details headers
      'FIELD': 'field',
      'VALUE': 'value',
      'SOURCE': 'source',

      // Relationships headers
      'RELATEDENTITY': 'relatedentity',
      'RELATIONSHIPTYPE': 'relationshiptype',
      'FOREIGNKEY': 'foreignkey',
      'DESCRIPTION': 'description',
      'STATUS': 'status',

      // Business Rules headers
      'ATTRIBUTENAME': 'attributename',
      'OPERATOR': 'operator',
      'ERRORMESSAGE': 'errormessage',

      // Enum Values headers
      'ENTITYATTRIBUTE': 'entityattribute',
      'ENUMNAME': 'enumname',
      'DISPLAY': 'display',
      'SORTORDER': 'sortorder',
      'ACTIVE': 'active',

      // Security Classification headers
      'CLASSIFICATION': 'classification',
      'PIITYPE': 'piitype',
      'ENCRYPTIONREQUIRED': 'encryptionrequired',
      'ENCRYPTIONTYPE': 'encryptiontype',
      'MASKINGREQUIRED': 'maskingrequired',
      'MASKINGPATTERN': 'maskingpattern',
      'ACCESSLEVEL': 'accesslevel',
      'AUDITTRAIL': 'audittrail',

      // System Permissions headers
      'PERMISSIONID': 'permissionid',
      'PERMISSIONNAME': 'permissionname',
      'PERMISSIONTYPE': 'permissiontype',
      'RESOURCEIDENTIFIER': 'resourceidentifier',
      'ACTIONS': 'actions',
      'SCOPE': 'scope',
      'NATURALLANGUAGE': 'naturallanguage',
      'VERSION': 'version',

      // Generic headers
      'TYPE': 'type',
      'NAME': 'name',
      'REQUIRED': 'required',

      // Process Ownership headers
      'ROLETYPE': 'roletype',
      'ASSIGNEDROLE': 'assignedrole',

      // Trigger Definition headers
      'TRIGGERTYPE': 'triggertype',
      'TRIGGERATTRIBUTES': 'triggerattributes',
      'TRIGGERCONDITION': 'triggercondition',
      'TRIGGERSCHEDULE': 'triggerschedule',

      // Local Objectives headers
      'LOID': 'loid',
      'OBJECTIVENAME': 'objectivename',
      'AGENTTYPE': 'agenttype',

      // Pathways Definitions headers
      'PATHWAYID': 'pathwayid',
      'PATHWAYNAME': 'pathwayname',

      // Pathways Detail headers
      'OBJECTIVE': 'objective',
      'ROUTETYPE': 'routetype',
      'ROUTINGCONDITIONS': 'routingconditions',

      // Business Rules headers
      'ENTITY': 'entity',
      'INPUTS': 'inputs',
      'OPERATION': 'operation',
      'OUTPUT': 'output',
      'VALIDATION': 'validation',

      // Validation Rules headers
      'RULENAME': 'rulename',
      'VALIDATIONTYPE': 'validationtype',
    };
  }

  /// Check if a field should be a dropdown
  bool _isDropdownField(String sectionTitle, String header) {
    final dropdownFields = {
      'Object Details': ['SOURCE'],
      'Entity Relationships': ['RELATIONSHIPTYPE', 'STATUS'],
      'Attribute Business Rules': ['OPERATOR', 'STATUS'],
      'Enumerated Values': ['ACTIVE'],
      'System Permissions': ['PERMISSIONTYPE', 'STATUS'],
      'Security Classification': [
        'CLASSIFICATION',
        'PIITYPE',
        'ENCRYPTIONREQUIRED',
        'ENCRYPTIONTYPE',
        'MASKINGREQUIRED',
        'ACCESSLEVEL',
        'AUDITTRAIL'
      ],
      'Process Ownership': ['SOURCE', 'ROLETYPE', 'ASSIGNEDROLE'],
      'Role Assignment': ['ROLETYPE', 'ASSIGNEDROLE'],
      'Trigger Definition': ['TRIGGERTYPE', 'TRIGGERSCHEDULE'],
      'Trigger Configuration': ['TRIGGERTYPE', 'TRIGGERSCHEDULE'],
      'Objective Management': ['AGENTTYPE'],
      'Pathway Definitions Configuration': ['STATUS'],
      'Pathway Routing Configuration': ['AGENTTYPE', 'ROUTETYPE'],
      'Business Rules Configuration': ['ROLETYPE', 'OPERATION', 'VALIDATION'],
      'Validation Rules Configuration': ['OPERATION', 'VALIDATIONTYPE'],
    };

    return dropdownFields[sectionTitle]?.contains(header) ?? false;
  }

  /// Get dropdown options for specific fields
  List<String> _getDropdownOptionsForField(String sectionTitle, String header) {
    switch (header) {
      case 'SOURCE':
        return [
          'Extracted',
          'Auto-generated',
          'Generated',
          'Inferred',
          'Manual',
          'Derived',
          'System'
        ];
      case 'RELATIONSHIPTYPE':
        return ['one-to-one', 'one-to-many', 'many-to-one', 'many-to-many'];
      case 'OPERATOR':
        return [
          'equals',
          'not_equals',
          'contains',
          'starts_with',
          'ends_with',
          'regex',
          'range'
        ];
      case 'PERMISSIONTYPE':
        return ['read', 'write', 'delete', 'execute', 'admin'];
      case 'CLASSIFICATION':
        return ['public', 'internal', 'confidential', 'restricted'];
      case 'PIITYPE':
        return ['none', 'personal', 'sensitive', 'financial', 'health'];
      case 'ROLETYPE':
        return ['Originator', 'Executive', 'Business Sponsor'];
      case 'ASSIGNEDROLE':
        return ['Customer', 'Business Executive'];
      case 'TRIGGERTYPE':
        return [
          'user-initiated',
          'system-initiated',
          'time-based',
          'event-driven'
        ];
      case 'TRIGGERSCHEDULE':
        return ['on-demand', 'scheduled', 'real-time', 'batch'];
      case 'AGENTTYPE':
        return ['SYSTEM', 'Human', 'Machine', 'Hybrid'];
      case 'ROUTETYPE':
        return ['Sequential', 'Parallel', 'Conditional', 'Loop'];
      case 'OPERATION':
        return [
          'conditional_logic',
          'validation_function',
          'transformation',
          'calculation'
        ];
      case 'VALIDATION':
        return [
          'PRE_INSERT',
          'PRE_UPDATE',
          'PRE_DELETE',
          'POST_INSERT',
          'POST_UPDATE'
        ];
      case 'VALIDATIONTYPE':
        return ['PRE_DEPLOY', 'POST_DEPLOY', 'RUNTIME', 'COMPILE_TIME'];
      case 'ENCRYPTIONREQUIRED':
      case 'MASKINGREQUIRED':
      case 'AUDITTRAIL':
      case 'ACTIVE':
      case 'REQUIRED':
        return ['Yes', 'No'];
      case 'ENCRYPTIONTYPE':
        return ['none', 'AES-256', 'RSA', 'SHA-256'];
      case 'ACCESSLEVEL':
        return ['public', 'protected', 'private', 'admin'];
      case 'STATUS':
        return ['Active', 'Inactive', 'Pending', 'Archived'];
      default:
        return ['Active', 'Inactive'];
    }
  }

  /// Get default dropdown value
  String _getDefaultDropdownValue(String sectionTitle, String header) {
    switch (header) {
      case 'SOURCE':
        // For Role Assignment, default to 'Inferred' to match existing data
        if (sectionTitle == 'Role Assignment' ||
            sectionTitle == 'Process Ownership') {
          return 'Inferred';
        }
        return 'Extracted';
      case 'RELATIONSHIPTYPE':
        return 'one-to-many';
      case 'OPERATOR':
        return 'equals';
      case 'PERMISSIONTYPE':
        return 'read';
      case 'CLASSIFICATION':
        return 'internal';
      case 'PIITYPE':
        return 'none';
      case 'ROLETYPE':
        return 'Originator';
      case 'ASSIGNEDROLE':
        return 'Customer';
      case 'TRIGGERTYPE':
        return 'user-initiated';
      case 'TRIGGERSCHEDULE':
        return 'on-demand';
      case 'AGENTTYPE':
        return 'SYSTEM';
      case 'ROUTETYPE':
        return 'Sequential';
      case 'OPERATION':
        return 'conditional_logic';
      case 'VALIDATION':
        return 'PRE_INSERT';
      case 'VALIDATIONTYPE':
        return 'PRE_DEPLOY';
      case 'ENCRYPTIONREQUIRED':
      case 'MASKINGREQUIRED':
      case 'AUDITTRAIL':
        return 'No';
      case 'ACTIVE':
      case 'REQUIRED':
        return 'Yes';
      case 'ENCRYPTIONTYPE':
        return 'none';
      case 'ACCESSLEVEL':
        return 'protected';
      case 'STATUS':
        return 'Active';
      default:
        return 'Active';
    }
  }

  /// Build enhanced form fields with proper layout
  /// Replicates the exact layout from extract_details_middle_static.dart
  Widget _buildEnhancedFormFields(
    BuildContext context,
    String sectionTitle,
    Map<String, TextEditingController> controllers,
    Map<String, String> dropdownValues,
    StateSetter setState,
  ) {
    final headers = _getDialogHeaders(sectionTitle);
    final headerToKeyMap = _getHeaderToKeyMapping();
    final widgets = <Widget>[];

    // Build fields in pairs for better layout
    for (int i = 0; i < headers.length; i += 2) {
      final leftHeader = headers[i];
      final leftKey = headerToKeyMap[leftHeader] ?? leftHeader.toLowerCase();

      Widget leftField = _buildDialogField(context, leftHeader, leftKey,
          sectionTitle, controllers, dropdownValues, setState);

      Widget? rightField;
      if (i + 1 < headers.length) {
        final rightHeader = headers[i + 1];
        final rightKey =
            headerToKeyMap[rightHeader] ?? rightHeader.toLowerCase();
        rightField = _buildDialogField(context, rightHeader, rightKey,
            sectionTitle, controllers, dropdownValues, setState);
      }

      if (rightField != null) {
        widgets.add(
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: leftField),
              const SizedBox(width: 24),
              Expanded(child: rightField),
            ],
          ),
        );
      } else {
        widgets.add(leftField);
      }

      if (i + 2 < headers.length) {
        widgets.add(const SizedBox(height: 20));
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  /// Build individual dialog field (text or dropdown)
  /// Replicates the exact field building from extract_details_middle_static.dart
  Widget _buildDialogField(
    BuildContext context,
    String header,
    String key,
    String sectionTitle,
    Map<String, TextEditingController> controllers,
    Map<String, String> dropdownValues,
    StateSetter setState,
  ) {
    final displayLabel = _formatHeaderForDisplay(header);

    if (_isDropdownField(sectionTitle, header)) {
      final options = _getDropdownOptionsForField(sectionTitle, header);

      // Ensure we have options and a valid current value
      if (options.isEmpty) {
        // Fallback to text field if no options available
        return _buildEditableFormField(
          context,
          displayLabel,
          controllers[key] ?? TextEditingController(),
        );
      }

      // Get current value, ensuring it exists in options
      String currentValue = dropdownValues[key] ?? '';
      if (!options.contains(currentValue)) {
        currentValue = options.first;
        // Update the dropdown values map with the valid value
        dropdownValues[key] = currentValue;
      }

      return _buildEditableDropdownField(
        context,
        displayLabel,
        options,
        currentValue,
        (value) {
          setState(() {
            dropdownValues[key] = value!;
          });
        },
      );
    } else {
      return _buildEditableFormField(
        context,
        displayLabel,
        controllers[key] ?? TextEditingController(),
      );
    }
  }

  /// Format header for display
  String _formatHeaderForDisplay(String header) {
    // Convert from UPPERCASE to Title Case
    return header
        .toLowerCase()
        .split('_')
        .map((word) =>
            word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : word)
        .join(' ');
  }

  /// Build editable form field
  /// Replicates the exact styling from extract_details_middle_static.dart
  Widget _buildEditableFormField(
    BuildContext context,
    String label,
    TextEditingController controller,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 12,
            ),
            hintText: 'Enter $label',
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[500],
            ),
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  /// Build editable dropdown field
  /// Replicates the exact styling from extract_details_middle_static.dart
  Widget _buildEditableDropdownField(
    BuildContext context,
    String label,
    List<String> options,
    String currentValue,
    ValueChanged<String?> onChanged,
  ) {
    // Ensure we have valid options and current value
    if (options.isEmpty) {
      return _buildEditableFormField(
        context,
        label,
        TextEditingController(text: currentValue),
      );
    }

    // Ensure current value is valid
    final validCurrentValue =
        options.contains(currentValue) ? currentValue : options.first;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: validCurrentValue,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 12,
            ),
          ),
          items: options.map((String option) {
            return DropdownMenuItem<String>(
              value: option,
              child: Text(
                option,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  /// Handle dialog form submission
  /// Replicates the exact submission logic from extract_details_middle_static.dart
  void _handleDialogSubmit(
    BuildContext context,
    String sectionTitle,
    Map<String, TextEditingController> controllers,
    Map<String, String> dropdownValues,
    bool isEditMode,
    int? editIndex,
  ) {
    // Validate required fields
    if (!_validateDialogFields(sectionTitle, controllers, dropdownValues)) {
      return;
    }

    // Create data map from form values
    final data = <String, String>{};
    controllers.forEach((key, controller) {
      data[key] = controller.text;
    });
    dropdownValues.forEach((key, value) {
      data[key] = value;
    });

    // For Role Assignment, automatically add Source field
    if (sectionTitle == 'Role Assignment') {
      data['source'] = 'Inferred';
    }

    // Update the table data
    setState(() {
      if (isEditMode && editIndex != null) {
        _updateTableData(sectionTitle, editIndex, data);
      } else {
        _addTableData(sectionTitle, data);
      }
    });

    Navigator.of(context).pop();
  }

  /// Validate dialog fields
  bool _validateDialogFields(
    String sectionTitle,
    Map<String, TextEditingController> controllers,
    Map<String, String> dropdownValues,
  ) {
    // Basic validation - ensure required fields are not empty
    for (final controller in controllers.values) {
      if (controller.text.trim().isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Please fill in all required fields'),
            backgroundColor: Colors.red[600],
          ),
        );
        return false;
      }
    }
    return true;
  }

  /// Update table data for edit mode
  void _updateTableData(
      String sectionTitle, int index, Map<String, String> data) {
    if (index < _tableData.length) {
      _tableData[index] = Map<String, dynamic>.from(data);
    }
  }

  /// Add new table data
  void _addTableData(String sectionTitle, Map<String, String> data) {
    _tableData.add(Map<String, dynamic>.from(data));
  }
}
